<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Test</title>
    <style>
        body {
            background: #121212;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .notification-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Notification System Test</h1>
    
    <div class="test-container">
        <h2>JavaScript Test</h2>
        <div id="jsTest">JavaScript not loaded</div>
    </div>
    
    <div class="test-container">
        <h2>Notifications Container</h2>
        <div id="notificationsList">
            <!-- Notifications will be inserted here -->
        </div>
    </div>
    
    <div class="test-container">
        <h2>Console Output</h2>
        <div id="consoleOutput"></div>
    </div>

    <script>
        // Override console.log to display on page
        const originalLog = console.log;
        const consoleOutput = document.getElementById('consoleOutput');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const div = document.createElement('div');
            div.textContent = args.join(' ');
            div.style.margin = '5px 0';
            div.style.padding = '5px';
            div.style.background = 'rgba(0, 255, 0, 0.1)';
            div.style.borderLeft = '3px solid green';
            consoleOutput.appendChild(div);
        };
        
        console.error = function(...args) {
            originalLog.apply(console, args);
            const div = document.createElement('div');
            div.textContent = 'ERROR: ' + args.join(' ');
            div.style.margin = '5px 0';
            div.style.padding = '5px';
            div.style.background = 'rgba(255, 0, 0, 0.1)';
            div.style.borderLeft = '3px solid red';
            consoleOutput.appendChild(div);
        };

        // Test basic JavaScript
        document.getElementById('jsTest').textContent = 'JavaScript is working!';
        console.log('🔔 Test page loaded');

        // Test notification creation
        function createTestNotification() {
            const notification = {
                id: 1,
                type: 'test',
                title: 'Test Notification',
                message: 'This is a test notification to verify the system is working',
                time: 'Just now',
                unread: true,
                icon: 'fas fa-bell',
                category: 'test',
                priority: 'high'
            };

            const html = `
                <div class="notification-item">
                    <h3>${notification.title}</h3>
                    <p>${notification.message}</p>
                    <small>${notification.time}</small>
                </div>
            `;

            document.getElementById('notificationsList').innerHTML = html;
            console.log('🔔 Test notification created');
        }

        // Create test notification after DOM loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔔 DOM loaded');
            createTestNotification();
        });
    </script>
</body>
</html>
