<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Create Playlist - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/createplaylist.css">
</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>

            <!-- Hamburger <PERSON>u Button -->
            <button type="button" class="hamburger" id="hamburger" aria-label="Toggle navigation menu" aria-expanded="false">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <ul class="menu" id="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu"
                    aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
    </header>

    <main id="main-content" class="container">
        <div class="create-playlist-page">
            <div class="page-header">
                <h1 class="playlist-title-gradient">Create Playlist</h1>
                <p class="page-description">Build your own playlist and add your favorite tracks</p>
            </div>

            <div class="playlist-creation-container">
                <!-- Playlist Details Section -->
                <div class="playlist-details-section">
                    <div class="section-header">
                        <h2><i class="fas fa-info-circle"></i> Playlist Details</h2>
                    </div>

                    <form class="create-playlist-form" id="createPlaylistForm" autocomplete="off">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="playlistName">Playlist Name *</label>
                                <input type="text" id="playlistName" name="playlistName" maxlength="50" required
                                       placeholder="Enter playlist name" aria-describedby="nameHelp">
                                <small id="nameHelp" class="form-help">Choose a memorable name for your playlist</small>
                                <div class="character-count">
                                    <span id="nameCount">0</span>/50
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="playlistPrivacy">Privacy</label>
                                <select id="playlistPrivacy" name="playlistPrivacy">
                                    <option value="public">Public - Anyone can see and follow</option>
                                    <option value="private">Private - Only you can see</option>
                                    <option value="unlisted">Unlisted - Only people with link can see</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="playlistDescription">Description <span class="optional">(optional)</span></label>
                            <textarea id="playlistDescription" name="playlistDescription" maxlength="200"
                                      placeholder="Describe your playlist - what's the vibe, when to listen, etc."
                                      aria-describedby="descHelp"></textarea>
                            <small id="descHelp" class="form-help">Help others discover your playlist with a good description</small>
                            <div class="character-count">
                                <span id="descCount">0</span>/200
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="playlistGenre">Primary Genre</label>
                                <select id="playlistGenre" name="playlistGenre">
                                    <option value="">Select a genre</option>
                                    <option value="pop">Pop</option>
                                    <option value="rock">Rock</option>
                                    <option value="hip-hop">Hip Hop</option>
                                    <option value="electronic">Electronic</option>
                                    <option value="jazz">Jazz</option>
                                    <option value="classical">Classical</option>
                                    <option value="country">Country</option>
                                    <option value="r&b">R&B</option>
                                    <option value="indie">Indie</option>
                                    <option value="alternative">Alternative</option>
                                    <option value="folk">Folk</option>
                                    <option value="reggae">Reggae</option>
                                    <option value="blues">Blues</option>
                                    <option value="metal">Metal</option>
                                    <option value="punk">Punk</option>
                                    <option value="mixed">Mixed/Various</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="playlistMood">Mood/Vibe</label>
                                <select id="playlistMood" name="playlistMood">
                                    <option value="">Select a mood</option>
                                    <option value="energetic">Energetic</option>
                                    <option value="chill">Chill</option>
                                    <option value="romantic">Romantic</option>
                                    <option value="party">Party</option>
                                    <option value="workout">Workout</option>
                                    <option value="study">Study/Focus</option>
                                    <option value="sad">Sad/Melancholy</option>
                                    <option value="happy">Happy/Upbeat</option>
                                    <option value="nostalgic">Nostalgic</option>
                                    <option value="aggressive">Aggressive</option>
                                    <option value="peaceful">Peaceful</option>
                                    <option value="motivational">Motivational</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="action-btn secondary-btn" id="cancelBtn">
                                <i class="fas fa-times"></i>
                                Cancel
                            </button>
                            <button type="submit" class="action-btn create-btn">
                                <i class="fas fa-plus-circle"></i>
                                Create Playlist
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Playlist Cover Section -->
                <div class="playlist-cover-section">
                    <div class="section-header">
                        <h2><i class="fas fa-image"></i> Playlist Cover</h2>
                    </div>

                    <div class="cover-upload-area">
                        <div class="cover-preview" id="coverPreview">
                            <div class="default-cover">
                                <i class="fas fa-music"></i>
                                <span>No cover selected</span>
                            </div>
                        </div>

                        <div class="cover-actions">
                            <label for="coverUpload" class="upload-btn">
                                <i class="fas fa-upload"></i>
                                Upload Cover
                            </label>
                            <input type="file" id="coverUpload" accept="image/*" hidden>

                            <button type="button" class="action-btn secondary-btn" id="generateCoverBtn">
                                <i class="fas fa-magic"></i>
                                Generate Cover
                            </button>
                        </div>

                        <div class="cover-help">
                            <small>Recommended: 300x300px, JPG or PNG, max 5MB</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <div id="form-success" class="form-message success-message hidden">
                <i class="fas fa-check-circle"></i>
                <span>Playlist created successfully!</span>
            </div>

            <div id="form-error" class="form-message error-message hidden">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorText">Something went wrong. Please try again.</span>
            </div>
        </div>

        <div id="aria-live-region" aria-live="polite" class="sr-only"></div>
    </main>
    <!-- Shared utilities -->
    <script src="js/utils.js"></script>
    <script src="js/createplaylist.js"></script>
</body>
</html>