<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/subscribe.css">
    <title>Choose Your Subscription | Banshee Music</title>
</head>


<body>

    <!-- Navbar removed for focused subscription flow -->

    <h1 class="welcome-header">Choose Your Subscription</h1>

    <div class="subscription-progress">
        <div class="progress-step active">
            <span class="step-number">1</span>
            <span class="step-text">Choose Plan</span>
        </div>
        <div class="progress-step">
            <span class="step-number">2</span>
            <span class="step-text">Payment</span>
        </div>
        <div class="progress-step">
            <span class="step-number">3</span>
            <span class="step-text">Confirmation</span>
        </div>
    </div>

    <main id="main-content" class="container">
        <section class="subscription-section">
            <div class="subscription-plans">
                <div class="card plan-card">
                    <div class="plan-header">
                        <h2>Free Account</h2>
                        <p class="price">
                            <span class="currency">$</span>
                            <span class="amount">0</span>
                            <span class="period">/month</span>
                        </p>
                    </div>
                    <div class="card-content plan-content">
                        <div class="text-content">
                            <ul class="features-list">
                                <li><i class="fas fa-check"></i> Enjoy Banshee with occasional ads</li>
                                <li><i class="fas fa-check"></i> Basic audio quality (128kbps)</li>
                                <li><i class="fas fa-check"></i> Limited skips (6 per hour)</li>
                                <li><i class="fas fa-check"></i> Mobile app access</li>
                            </ul>
                        </div>
                        <button type="button" class="button" data-plan="free">Get Started</button>
                    </div>
                </div>

                <div class="card plan-card featured">
                    <div class="popular-tag">Most Popular</div>
                    <div class="plan-header">
                        <h2>Premium</h2>
                        <p class="price">
                            <span class="currency">$</span>
                            <span class="amount">2.99</span>
                            <span class="period">/month</span>
                        </p>
                    </div>
                    <div class="card-content plan-content">
                        <div class="text-content">
                            <ul class="features-list">
                                <li><i class="fas fa-check"></i> Ad-free listening experience</li>
                                <li><i class="fas fa-check"></i> High-quality audio (320kbps)</li>
                                <li><i class="fas fa-check"></i> Unlimited skips</li>
                                <li><i class="fas fa-check"></i> Offline mode</li>
                                <li><i class="fas fa-check"></i> Cross-platform sync</li>
                                <li><i class="fas fa-check"></i> Exclusive content access</li>
                            </ul>
                        </div>
                        <button type="button" class="button" data-plan="premium">Subscribe Now</button>
                    </div>
                </div>

                <div class="card plan-card">
                    <div class="plan-header">
                        <h2>Artist Account</h2>
                        <p class="price">
                            <span class="currency">$</span>
                            <span class="amount">4.99</span>
                            <span class="period">/month</span>
                        </p>
                    </div>
                    <div class="card-content plan-content">
                        <div class="text-content">
                            <ul class="features-list">
                                <li><i class="fas fa-check"></i> All Premium features included</li>
                                <li><i class="fas fa-check"></i> Upload unlimited tracks</li>
                                <li><i class="fas fa-check"></i> Advanced analytics dashboard</li>
                                <li><i class="fas fa-check"></i> Promotional tools</li>
                                <li><i class="fas fa-check"></i> Direct fan engagement</li>
                                <li><i class="fas fa-check"></i> Custom artist profile</li>
                            </ul>
                        </div>
                        <button type="button" class="button" data-plan="artist">Start Creating</button>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-info">
                <p class="copyright">&copy; 2024 Banshee Music App. All rights reserved.</p>
                <nav class="footer-nav">
                    <a href="privacy.html">Privacy Policy</a>
                    <a href="terms.html">Terms of Service</a>
                    <a href="contact.html">Contact Us</a>
                </nav>
            </div>
        </div>
    </footer>

    <!-- Back to top button -->
    <button id="backToTop" class="back-to-top" aria-label="Back to top" type="button">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Loader for API calls (global loader) -->
    <div id="api-loader" class="loader-container">
        <div class="loader"></div>
    </div>

    <!-- Subscribe page logic -->
    <script src="js/subscribe.js"></script>
    <!-- Back to top button logic -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopButton = document.getElementById('backToTop');
            if (backToTopButton) {
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 300) {
                        backToTopButton.classList.add('visible');
                    } else {
                        backToTopButton.classList.remove('visible');
                    }
                });
                backToTopButton.addEventListener('click', function() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        });
    </script>
</body>
</html>