// Enhanced Confirmation Page with Backend Integration
document.addEventListener('DOMContentLoaded', () => {
    // Load subscription and transaction details
    loadConfirmationDetails();

    // Handle receipt download
    const downloadButton = document.querySelector('.download-receipt');
    if (downloadButton) {
        downloadButton.addEventListener('click', generateReceipt);
    }
});

// Enhanced confirmation details loading with backend integration
async function loadConfirmationDetails() {
    // Get data from session storage
    const subscriptionId = sessionStorage.getItem('subscriptionId');
    const transactionId = sessionStorage.getItem('transactionId');
    const subscriptionDataJson = sessionStorage.getItem('subscriptionData');
    const transactionDataJson = sessionStorage.getItem('transactionData');
    const selectedPlanJson = sessionStorage.getItem('selectedPlan');

    if (!selectedPlanJson) {
        // If no plan is selected, redirect to subscription page
        window.location.href = 'subscribe.html';
        return;
    }

    try {
        console.log('📋 Loading confirmation details...');
        
        // Parse stored data
        const selectedPlan = JSON.parse(selectedPlanJson);
        let subscriptionDetails = null;
        let transactionDetails = null;

        // Try to get details from backend if we have IDs
        if (subscriptionId) {
            try {
                const subResponse = await fetch(`http://localhost:3001/api/subscriptions/${subscriptionId}`);
                if (subResponse.ok) {
                    const subResult = await subResponse.json();
                    subscriptionDetails = subResult.subscription;
                    console.log('✅ Subscription details loaded from backend');
                }
            } catch (error) {
                console.log('Could not fetch subscription from backend, using stored data');
            }
        }

        if (transactionId) {
            try {
                const txnResponse = await fetch(`http://localhost:3001/api/transactions/${transactionId}`);
                if (txnResponse.ok) {
                    const txnResult = await txnResponse.json();
                    transactionDetails = txnResult.transaction;
                    console.log('✅ Transaction details loaded from backend');
                }
            } catch (error) {
                console.log('Could not fetch transaction from backend, using stored data');
            }
        }

        // Fallback to stored data if backend calls failed
        if (!subscriptionDetails && subscriptionDataJson) {
            subscriptionDetails = JSON.parse(subscriptionDataJson);
            console.log('📦 Using stored subscription data');
        }
        if (!transactionDetails && transactionDataJson) {
            transactionDetails = JSON.parse(transactionDataJson);
            console.log('📦 Using stored transaction data');
        }

        // Fallback to mock data if nothing is available
        if (!subscriptionDetails) {
            const nextBillingDate = sessionStorage.getItem('nextBillingDate');
            subscriptionDetails = {
                currentPeriodEnd: nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'active'
            };
            console.log('🔄 Using fallback subscription data');
        }

        // Update UI elements
        updateConfirmationUI(selectedPlan, subscriptionDetails, transactionDetails);
        
        console.log('✅ Confirmation details loaded successfully!');
    } catch (error) {
        console.error('Error loading confirmation details:', error);
        showToast('There was an error loading your subscription details.', 'error');
        
        // Fallback UI update with minimal data
        try {
            const selectedPlan = JSON.parse(selectedPlanJson);
            updateConfirmationUI(selectedPlan, {}, {});
        } catch (fallbackError) {
            console.error('Fallback UI update failed:', fallbackError);
        }
    }
}

function updateConfirmationUI(selectedPlan, subscriptionDetails, transactionDetails) {
    // Update UI elements - handle both ID-based and class-based selectors
    const planNameElement = document.getElementById('planName') || document.querySelector('.plan-name');
    const billingCycleElement = document.getElementById('billingCycle') || document.querySelector('.billing-cycle');
    const nextBillingElement = document.getElementById('nextBilling') || document.querySelector('.next-billing');
    const amountElement = document.getElementById('amount') || document.querySelector('.amount');
    const userEmailElement = document.getElementById('userEmail') || document.querySelector('.user-email');
    const pageTitleElement = document.querySelector('h1') || document.querySelector('.confirmation-title');
    const transactionIdElement = document.querySelector('.transaction-id');
    const paymentMethodElement = document.querySelector('.payment-method');

    // Update plan name
    if (planNameElement) {
        planNameElement.textContent = selectedPlan.name;
    }

    // Update billing cycle
    if (billingCycleElement) {
        billingCycleElement.textContent = selectedPlan.interval === 'month' ? 'Monthly' : 'Yearly';
    }

    // Format next billing date
    if (nextBillingElement) {
        const nextBillingDate = subscriptionDetails.currentPeriodEnd || 
                               sessionStorage.getItem('nextBillingDate') ||
                               new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
        
        const nextBillingDateObj = new Date(nextBillingDate);
        nextBillingElement.textContent = nextBillingDateObj.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Update amount
    if (amountElement) {
        amountElement.textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
    }

    // Update user email
    if (userEmailElement) {
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        const user = userJson ? JSON.parse(userJson) : { email: '<EMAIL>' };
        userEmailElement.textContent = user.email;
    }

    // Update page title based on plan
    if (pageTitleElement) {
        pageTitleElement.textContent = `Welcome to Banshee ${selectedPlan.name}!`;
    }

    // Update transaction details if available
    if (transactionIdElement && transactionDetails) {
        transactionIdElement.textContent = transactionDetails.id || sessionStorage.getItem('transactionId') || 'N/A';
    }

    if (paymentMethodElement && transactionDetails && transactionDetails.paymentMethod) {
        const pm = transactionDetails.paymentMethod;
        paymentMethodElement.textContent = `${pm.brand || 'Card'} ending in ${pm.last4 || '****'}`;
    }

    // Update features based on plan
    updateFeaturesList(selectedPlan.name ? selectedPlan.name.toLowerCase() : 'premium');
}

function updateFeaturesList(planKey) {
    // This would be more dynamic in a real application
    const featuresList = document.querySelector('.feature-list');

    if (!featuresList) return;

    // Clear existing features
    featuresList.innerHTML = '';

    let features = [];
    
    switch (planKey) {
        case 'free':
        case 'free account':
            features = [
                'Enjoy Banshee with occasional ads',
                'Basic audio quality (128kbps)',
                'Limited skips (6 per hour)',
                'Mobile app access'
            ];
            break;
        case 'premium':
            features = [
                'Ad-free listening experience',
                'High-quality audio (320kbps)',
                'Unlimited skips',
                'Offline mode',
                'Cross-platform sync',
                'Exclusive content access'
            ];
            break;
        case 'creator':
        case 'artist':
        case 'artist account':
            features = [
                'All Premium features included',
                'Upload unlimited tracks',
                'Advanced analytics dashboard',
                'Promotional tools',
                'Direct fan engagement',
                'Custom artist profile'
            ];
            break;
        default:
            features = [
                'Premium music streaming',
                'High-quality audio',
                'Unlimited access'
            ];
    }

    features.forEach(feature => {
        const li = document.createElement('li');
        li.innerHTML = `<i class="fas fa-check"></i> ${feature}`;
        featuresList.appendChild(li);
    });
}

async function generateReceipt() {
    try {
        console.log('📄 Generating receipt...');
        
        // Get transaction and subscription data
        const transactionId = sessionStorage.getItem('transactionId');
        const selectedPlanJson = sessionStorage.getItem('selectedPlan');
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        
        if (!transactionId || !selectedPlanJson) {
            showToast('Receipt data not available', 'error');
            return;
        }

        const selectedPlan = JSON.parse(selectedPlanJson);
        const user = userJson ? JSON.parse(userJson) : { email: '<EMAIL>', name: 'User' };
        
        // Create receipt content
        const receiptContent = `
BANSHEE MUSIC - RECEIPT
========================

Transaction ID: ${transactionId}
Date: ${new Date().toLocaleDateString()}
Time: ${new Date().toLocaleTimeString()}

Customer Information:
Name: ${user.name || user.username || 'User'}
Email: ${user.email}

Subscription Details:
Plan: ${selectedPlan.name}
Amount: $${selectedPlan.price.toFixed(2)}
Billing Cycle: ${selectedPlan.interval}

Payment Status: Completed
Next Billing: ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}

Thank you for choosing Banshee Music!
For support, contact: <EMAIL>
        `;

        // Create and download receipt file
        const blob = new Blob([receiptContent], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `banshee-receipt-${transactionId}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showToast('Receipt downloaded successfully!', 'success');
    } catch (error) {
        console.error('Error generating receipt:', error);
        showToast('Failed to generate receipt', 'error');
    }
}

function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'times-circle'}"></i>
        <span>${message}</span>
    `;
    
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '600',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        display: 'flex',
        alignItems: 'center',
        gap: '0.5rem',
        minWidth: '300px',
        background: type === 'success' 
            ? 'linear-gradient(135deg, #4CAF50, #45a049)'
            : 'linear-gradient(135deg, #f44336, #d32f2f)',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
    });

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Auto remove
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}
