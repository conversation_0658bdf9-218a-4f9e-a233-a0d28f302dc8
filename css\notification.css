:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-color: #fff;
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* Notification Colors */
    --notification-bg: rgba(20, 22, 30, 0.95);
    --notification-hover: rgba(255,255,255,0.08);
    --button-gradient-start: #00E0FF;
    --button-gradient-end: #FF006E;
    --delete-gradient: linear-gradient(90deg, #ff4646 0%, #ff006e 100%);
    --delete-gradient-start: #ff4646;
    --delete-gradient-end: #ff006e;
}

/* Add proper spacing for main content */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Gradient heading for notifications page */
.notification-title-gradient {
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    margin-top: 0.5rem;
    text-align: center;
    letter-spacing: 1.5px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: none;
    padding-bottom: 0.3em;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    left: -999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--neon-blue);
    color: #fff;
    z-index: 3000;
    padding: 0.5em 1em;
    border-radius: 8px;
    font-weight: 700;
    transition: left 0.2s;
}
.skip-link:focus {
    left: 10px;
    top: 10px;
    width: auto;
    height: auto;
    outline: 2px solid var(--cosmic-pink);
}

/* Main container styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 20px 40px;
    min-height: 100vh;
}

/* Notifications page layout */
.notifications-page {
    width: 100%;
}

/* Page header */
.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-top: 0.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Notifications controls */
.notifications-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
    flex-wrap: wrap;
}

.controls-left {
    flex: 1;
}

.controls-right {
    display: flex;
    gap: 0.75rem;
}

/* Filter tabs */
.filter-tabs {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.25rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-tab {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    font-size: 0.95rem;
}

.filter-tab:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.08);
}

.filter-tab.active {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 224, 255, 0.3);
}

/* Action buttons */
.action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.mark-all-btn:hover {
    border-color: var(--neon-blue);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.3);
}

.clear-all-btn:hover {
    border-color: var(--error-color);
    box-shadow: 0 5px 15px rgba(255, 70, 70, 0.3);
}

/* Notifications container */
.notifications-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    overflow: hidden;
    min-height: 400px;
}

.notifications-list {
    padding: 1rem;
}

/* Individual notification styles */
.notification-item {
    background: var(--notification-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all var(--transition-normal);
    position: relative;
    cursor: pointer;
}

.notification-item:hover {
    background: var(--notification-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.notification-item.unread {
    border-left: 4px solid var(--neon-blue);
    background: rgba(0, 224, 255, 0.05);
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 8px;
    height: 8px;
    background: var(--neon-blue);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--neon-blue);
}

/* Notification content */
.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.notification-icon.music {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--electric-violet));
}

.notification-icon.social {
    background: linear-gradient(45deg, var(--neon-blue), var(--cyber-lime));
}

.notification-icon.system {
    background: linear-gradient(45deg, var(--electric-violet), var(--cosmic-pink));
}

.notification-content {
    flex: 1;
}

.notification-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.notification-time {
    opacity: 0.8;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
}

.notification-action {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.8rem;
}

.notification-action:hover {
    color: var(--text-primary);
    border-color: var(--neon-blue);
}

.notification-action.delete:hover {
    border-color: var(--error-color);
    color: var(--error-color);
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-state h3 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .notifications-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .controls-right {
        justify-content: center;
    }

    .filter-tabs {
        justify-content: center;
    }

    .filter-tab {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .action-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .notification-item {
        padding: 1rem;
    }

    .notification-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .notification-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
    }

    .notification-title {
        font-size: 0.95rem;
    }

    .notification-message {
        font-size: 0.85rem;
    }

    .notification-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .empty-state {
        padding: 3rem 1rem;
    }

    .empty-icon {
        font-size: 3rem;
    }

    .empty-state h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .notification-title-gradient {
        font-size: 1.8rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .filter-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .filter-tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
        flex: 1;
        min-width: 70px;
    }

    .controls-right {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        justify-content: center;
        padding: 0.75rem;
    }

    .notification-item {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .notification-icon {
        width: 30px;
        height: 30px;
        margin-right: 0.5rem;
    }

    .notification-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .notification-action {
        text-align: center;
        padding: 0.4rem 0.75rem;
    }
}

/* Animation for notification interactions */
@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes notificationSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(20px);
    }
}

.notification-item.slide-in {
    animation: notificationSlideIn 0.3s ease-out;
}

.notification-item.slide-out {
    animation: notificationSlideOut 0.3s ease-out;
}

/* Focus styles for accessibility */
.filter-tab:focus,
.action-btn:focus,
.notification-item:focus,
.notification-action:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .notification-item {
        border: 2px solid var(--text-primary);
    }

    .filter-tab.active {
        background: var(--text-primary);
        color: var(--background-primary);
    }

    .action-btn {
        border: 2px solid var(--text-primary);
    }
}
