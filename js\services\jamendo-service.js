// Jamendo API Service for Free Music Integration
// Provides access to 600,000+ free Creative Commons licensed tracks
class JamendoService {
    constructor() {
        this.baseUrl = 'https://api.jamendo.com/v3.0';
        this.clientId = 'YOUR_JAMENDO_CLIENT_ID'; // Replace with actual client ID from devportal.jamendo.com
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
        this.rateLimitDelay = 200; // 200ms between requests (more conservative than <PERSON><PERSON>)
        this.lastRequestTime = 0;
        
        // Default audio format preferences
        this.defaultAudioFormat = 'mp32'; // VBR, good quality
        this.defaultImageSize = 300;
    }

    // Rate limiting helper
    async waitForRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.rateLimitDelay) {
            await new Promise(resolve => setTimeout(resolve, this.rateLimitDelay - timeSinceLastRequest));
        }
        this.lastRequestTime = Date.now();
    }

    // Check if cached data is still valid
    isCacheValid(cacheKey) {
        if (!this.cache.has(cacheKey)) return false;
        const cached = this.cache.get(cacheKey);
        return Date.now() - cached.timestamp < this.cacheTimeout;
    }

    // Make API request with caching and error handling
    async makeRequest(endpoint, cacheKey = null) {
        // Check cache first
        if (cacheKey && this.isCacheValid(cacheKey)) {
            console.log(`🎵 Using cached Jamendo data: ${endpoint}`);
            return this.cache.get(cacheKey).data;
        }

        await this.waitForRateLimit();

        try {
            console.log(`🎵 Fetching from Jamendo: ${endpoint}`);
            const url = `${this.baseUrl}/${endpoint}${endpoint.includes('?') ? '&' : '?'}client_id=${this.clientId}&format=json`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Jamendo API error: ${response.status}`);
            }

            const data = await response.json();

            // Check for API errors
            if (data.headers && data.headers.status === 'failed') {
                throw new Error(`Jamendo API: ${data.headers.error_message}`);
            }

            // Cache the result
            if (cacheKey) {
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
            }

            return data;
        } catch (error) {
            console.error(`❌ Jamendo API request failed for ${endpoint}:`, error);
            throw error;
        }
    }

    // Get popular/featured tracks
    async getFeaturedTracks(limit = 25, genre = null) {
        try {
            let endpoint = `tracks?featured=1&limit=${limit}&audioformat=${this.defaultAudioFormat}&imagesize=${this.defaultImageSize}&include=musicinfo`;
            
            if (genre) {
                endpoint += `&tags=${encodeURIComponent(genre)}`;
            }
            
            const cacheKey = `featured_${genre || 'all'}_${limit}`;
            const data = await this.makeRequest(endpoint, cacheKey);
            return this.formatTracksData(data);
        } catch (error) {
            console.error('Failed to fetch Jamendo featured tracks:', error);
            return { tracks: [], total: 0 };
        }
    }

    // Search for tracks, artists, albums
    async search(query, type = 'track', limit = 25) {
        if (!query || query.trim().length < 2) {
            return { data: [], total: 0 };
        }

        try {
            let endpoint;
            const cacheKey = `search_${type}_${query}_${limit}`;

            switch (type) {
                case 'track':
                    endpoint = `tracks?search=${encodeURIComponent(query)}&limit=${limit}&audioformat=${this.defaultAudioFormat}&imagesize=${this.defaultImageSize}&include=musicinfo`;
                    break;
                case 'artist':
                    endpoint = `artists?search=${encodeURIComponent(query)}&limit=${limit}`;
                    break;
                case 'album':
                    endpoint = `albums?search=${encodeURIComponent(query)}&limit=${limit}&imagesize=${this.defaultImageSize}`;
                    break;
                default:
                    // For 'all' type, search tracks by default
                    endpoint = `tracks?search=${encodeURIComponent(query)}&limit=${limit}&audioformat=${this.defaultAudioFormat}&imagesize=${this.defaultImageSize}&include=musicinfo`;
            }

            const data = await this.makeRequest(endpoint, cacheKey);
            return this.formatSearchData(data, type);
        } catch (error) {
            console.error(`Failed to search Jamendo for "${query}":`, error);
            return { data: [], total: 0 };
        }
    }

    // Get tracks by genre/tags
    async getTracksByGenre(genre, limit = 25, featured = true) {
        try {
            let endpoint = `tracks?tags=${encodeURIComponent(genre)}&limit=${limit}&audioformat=${this.defaultAudioFormat}&imagesize=${this.defaultImageSize}&include=musicinfo`;
            
            if (featured) {
                endpoint += '&featured=1';
            }
            
            const cacheKey = `genre_${genre}_${limit}_${featured}`;
            const data = await this.makeRequest(endpoint, cacheKey);
            return this.formatTracksData(data);
        } catch (error) {
            console.error(`Failed to fetch tracks for genre "${genre}":`, error);
            return { tracks: [], total: 0 };
        }
    }

    // Get popular playlists
    async getPlaylists(limit = 20) {
        try {
            const endpoint = `playlists?limit=${limit}&order=creationdate_desc`;
            const cacheKey = `playlists_${limit}`;
            const data = await this.makeRequest(endpoint, cacheKey);
            return this.formatPlaylistsData(data);
        } catch (error) {
            console.error('Failed to fetch Jamendo playlists:', error);
            return { playlists: [], total: 0 };
        }
    }

    // Get tracks from a specific playlist
    async getPlaylistTracks(playlistId, limit = 50) {
        try {
            const endpoint = `playlists/tracks?id=${playlistId}&limit=${limit}&audioformat=${this.defaultAudioFormat}&imagesize=${this.defaultImageSize}&include=musicinfo`;
            const cacheKey = `playlist_tracks_${playlistId}_${limit}`;
            const data = await this.makeRequest(endpoint, cacheKey);
            return this.formatTracksData(data);
        } catch (error) {
            console.error(`Failed to fetch tracks for playlist ${playlistId}:`, error);
            return { tracks: [], total: 0 };
        }
    }

    // Get artist details
    async getArtist(artistId) {
        try {
            const [artist, tracks] = await Promise.all([
                this.makeRequest(`artists?id=${artistId}`, `artist_${artistId}`),
                this.makeRequest(`artists/tracks?id=${artistId}&limit=10&audioformat=${this.defaultAudioFormat}&imagesize=${this.defaultImageSize}&include=musicinfo`, `artist_tracks_${artistId}`)
            ]);

            return this.formatArtistData(artist, tracks);
        } catch (error) {
            console.error(`Failed to fetch artist ${artistId}:`, error);
            return null;
        }
    }

    // Format tracks data for consistency with existing app structure
    formatTracksData(data) {
        if (!data || !data.results) {
            return { tracks: [], total: 0 };
        }

        const tracks = data.results.map(track => ({
            id: track.id,
            title: track.name,
            artist: track.artist_name,
            artistId: track.artist_id,
            album: track.album_name || 'Single',
            albumId: track.album_id,
            duration: track.duration,
            url: track.audio, // Direct playable URL
            downloadUrl: track.audiodownload,
            downloadAllowed: track.audiodownload_allowed,
            cover: track.image || track.album_image,
            releaseDate: track.releasedate,
            genre: track.musicinfo?.tags?.genres?.[0] || 'Unknown',
            tags: track.musicinfo?.tags || {},
            license: track.license_ccurl,
            shareUrl: track.shareurl,
            waveform: track.waveform ? JSON.parse(track.waveform) : null,
            source: 'jamendo'
        }));

        return {
            tracks,
            total: data.headers?.results_count || tracks.length
        };
    }

    // Format search data
    formatSearchData(data, type) {
        if (!data || !data.results) {
            return { data: [], total: 0 };
        }

        let formattedData;
        switch (type) {
            case 'track':
                formattedData = this.formatTracksData(data).tracks;
                break;
            case 'artist':
                formattedData = data.results.map(artist => ({
                    id: artist.id,
                    name: artist.name,
                    image: artist.image,
                    shareUrl: artist.shareurl,
                    source: 'jamendo'
                }));
                break;
            case 'album':
                formattedData = data.results.map(album => ({
                    id: album.id,
                    title: album.name,
                    artist: album.artist_name,
                    artistId: album.artist_id,
                    cover: album.image,
                    releaseDate: album.releasedate,
                    shareUrl: album.shareurl,
                    source: 'jamendo'
                }));
                break;
            default:
                formattedData = this.formatTracksData(data).tracks;
        }

        return {
            data: formattedData,
            total: data.headers?.results_count || formattedData.length
        };
    }

    // Format playlists data
    formatPlaylistsData(data) {
        if (!data || !data.results) {
            return { playlists: [], total: 0 };
        }

        const playlists = data.results.map(playlist => ({
            id: playlist.id,
            name: playlist.name,
            creator: playlist.user_name,
            creatorId: playlist.user_id,
            creationDate: playlist.creationdate,
            shareUrl: playlist.shareurl,
            downloadUrl: playlist.zip,
            source: 'jamendo'
        }));

        return {
            playlists,
            total: data.headers?.results_count || playlists.length
        };
    }

    // Format artist data
    formatArtistData(artistData, tracksData) {
        if (!artistData || !artistData.results || artistData.results.length === 0) {
            return null;
        }

        const artist = artistData.results[0];
        const tracks = this.formatTracksData(tracksData).tracks;

        return {
            id: artist.id,
            name: artist.name,
            image: artist.image,
            shareUrl: artist.shareurl,
            topTracks: tracks,
            source: 'jamendo'
        };
    }

    // Health check method
    async healthCheck() {
        try {
            console.log('🎵 Checking Jamendo API health...');

            // If not configured, return false but don't make API call
            if (!this.isConfigured()) {
                console.log('🎵 Jamendo API: Not configured (no client ID)');
                return false;
            }

            const data = await this.makeRequest('tracks?limit=1', null);
            const isHealthy = data && data.headers && data.headers.status === 'success';
            console.log(`🎵 Jamendo API health: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
            return isHealthy;
        } catch (error) {
            console.error('❌ Jamendo API health check failed:', error);
            return false;
        }
    }

    // Demo mode - returns sample data for testing
    getDemoTracks() {
        return {
            tracks: [
                {
                    id: 'demo-1',
                    title: 'Demo Track 1',
                    artist: 'Demo Artist',
                    album: 'Demo Album',
                    duration: 180,
                    url: 'demo/demo-track.mp3',
                    cover: 'imgs/album-01.png',
                    genre: 'Demo',
                    playable: true,
                    source: 'demo'
                },
                {
                    id: 'demo-2',
                    title: 'Demo Track 2',
                    artist: 'Another Demo Artist',
                    album: 'Demo Collection',
                    duration: 210,
                    url: 'demo/demo-track.mp3',
                    cover: 'imgs/album-02.png',
                    genre: 'Demo',
                    playable: true,
                    source: 'demo'
                }
            ],
            total: 2
        };
    }

    // Get configuration status
    isConfigured() {
        return this.clientId && this.clientId !== 'YOUR_JAMENDO_CLIENT_ID';
    }

    // Set client ID (for dynamic configuration)
    setClientId(clientId) {
        this.clientId = clientId;
        console.log('🎵 Jamendo client ID updated');
    }
}

// Create global instance
window.jamendoService = new JamendoService();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JamendoService;
}
