<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banshee Performance Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #0D1117 0%, #161B22 100%);
            color: #fff;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(45deg, #ff006e, #00e0ff);
            border-radius: 15px;
            color: white;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }
        .test-card h3 {
            color: #00e0ff;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }
        .good { color: #4CAF50; }
        .warning { color: #FF9800; }
        .error { color: #F44336; }
        .btn {
            background: linear-gradient(45deg, #ff006e, #00e0ff);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff006e, #00e0ff);
            transition: width 0.3s ease;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff006e;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .results-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 10px;
            border-left: 4px solid #00e0ff;
        }
        .page-test {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎵 Banshee Music App - Performance Report</h1>
        <p>Comprehensive testing for mobile responsiveness and performance optimization</p>
    </div>

    <div class="test-grid">
        <div class="test-card">
            <h3><i class="fas fa-tachometer-alt"></i> Core Performance Metrics</h3>
            <div class="metric">
                <span>Page Load Time</span>
                <span class="metric-value" id="loadTime">Testing...</span>
            </div>
            <div class="metric">
                <span>API Response Time</span>
                <span class="metric-value" id="apiTime">Testing...</span>
            </div>
            <div class="metric">
                <span>DOM Nodes</span>
                <span class="metric-value" id="domNodes">Testing...</span>
            </div>
            <div class="metric">
                <span>Memory Usage</span>
                <span class="metric-value" id="memoryUsage">Testing...</span>
            </div>
            <button class="btn" onclick="runCoreTests()">🚀 Run Core Tests</button>
        </div>

        <div class="test-card">
            <h3><i class="fas fa-mobile-alt"></i> Mobile Responsiveness</h3>
            <div class="metric">
                <span>Hamburger Menu</span>
                <span class="metric-value" id="hamburgerTest">Not Tested</span>
            </div>
            <div class="metric">
                <span>Touch Targets</span>
                <span class="metric-value" id="touchTargets">Not Tested</span>
            </div>
            <div class="metric">
                <span>Viewport Scaling</span>
                <span class="metric-value" id="viewportTest">Not Tested</span>
            </div>
            <div class="metric">
                <span>Text Readability</span>
                <span class="metric-value" id="textTest">Not Tested</span>
            </div>
            <button class="btn" onclick="runMobileTests()">📱 Test Mobile UX</button>
        </div>

        <div class="test-card">
            <h3><i class="fas fa-server"></i> API Performance</h3>
            <div id="apiResults">
                <div class="metric">
                    <span>Library API</span>
                    <span class="metric-value">Ready</span>
                </div>
                <div class="metric">
                    <span>Search API</span>
                    <span class="metric-value">Ready</span>
                </div>
                <div class="metric">
                    <span>Home API</span>
                    <span class="metric-value">Ready</span>
                </div>
            </div>
            <button class="btn" onclick="runAPITests()">🔗 Test All APIs</button>
        </div>

        <div class="test-card">
            <h3><i class="fas fa-globe"></i> Cross-Browser Check</h3>
            <div class="metric">
                <span>User Agent</span>
                <span class="metric-value" id="userAgent">Detecting...</span>
            </div>
            <div class="metric">
                <span>CSS Support</span>
                <span class="metric-value" id="cssSupport">Checking...</span>
            </div>
            <div class="metric">
                <span>JavaScript Features</span>
                <span class="metric-value" id="jsFeatures">Checking...</span>
            </div>
            <button class="btn" onclick="runBrowserTests()">🌐 Check Compatibility</button>
        </div>
    </div>

    <div class="results-section">
        <h3>📊 Page-by-Page Testing Results</h3>
        <div id="pageTestResults">
            <p>Click "Test All Pages" to run comprehensive page testing...</p>
        </div>
        <button class="btn" onclick="testAllPages()">📄 Test All Pages</button>
        <button class="btn" onclick="generateReport()">📋 Generate Full Report</button>
    </div>

    <div class="results-section">
        <h3>🎯 Recommendations</h3>
        <div id="recommendations">
            <p>Run tests to get personalized recommendations...</p>
        </div>
    </div>

    <script>
        // Performance testing functions
        async function runCoreTests() {
            // Page load time
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            document.getElementById('loadTime').textContent = `${loadTime}ms`;
            document.getElementById('loadTime').className = loadTime < 2000 ? 'good' : loadTime < 4000 ? 'warning' : 'error';

            // DOM nodes
            const domNodes = document.getElementsByTagName('*').length;
            document.getElementById('domNodes').textContent = domNodes;
            document.getElementById('domNodes').className = domNodes < 1000 ? 'good' : domNodes < 2000 ? 'warning' : 'error';

            // Memory usage
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1048576);
                document.getElementById('memoryUsage').textContent = `${memoryMB}MB`;
                document.getElementById('memoryUsage').className = memoryMB < 50 ? 'good' : memoryMB < 100 ? 'warning' : 'error';
            } else {
                document.getElementById('memoryUsage').textContent = 'Not Available';
            }

            // API response time
            await testAPIResponse();
        }

        async function testAPIResponse() {
            try {
                const startTime = performance.now();
                const response = await fetch('http://localhost:3001/api/home/<USER>');
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                document.getElementById('apiTime').textContent = `${responseTime}ms`;
                document.getElementById('apiTime').className = responseTime < 300 ? 'good' : responseTime < 500 ? 'warning' : 'error';
            } catch (error) {
                document.getElementById('apiTime').textContent = 'Error';
                document.getElementById('apiTime').className = 'error';
            }
        }

        function runMobileTests() {
            // Test hamburger menu existence
            const hamburger = document.querySelector('.hamburger');
            document.getElementById('hamburgerTest').textContent = hamburger ? 'Present' : 'Missing';
            document.getElementById('hamburgerTest').className = hamburger ? 'good' : 'error';

            // Test viewport meta tag
            const viewport = document.querySelector('meta[name="viewport"]');
            document.getElementById('viewportTest').textContent = viewport ? 'Configured' : 'Missing';
            document.getElementById('viewportTest').className = viewport ? 'good' : 'error';

            // Test touch targets (simplified)
            const buttons = document.querySelectorAll('button, a');
            let touchFriendly = 0;
            buttons.forEach(btn => {
                const rect = btn.getBoundingClientRect();
                if (rect.height >= 44 && rect.width >= 44) touchFriendly++;
            });
            const percentage = Math.round((touchFriendly / buttons.length) * 100);
            document.getElementById('touchTargets').textContent = `${percentage}% Good`;
            document.getElementById('touchTargets').className = percentage > 80 ? 'good' : percentage > 60 ? 'warning' : 'error';

            // Test text readability
            const bodyStyle = window.getComputedStyle(document.body);
            const fontSize = parseInt(bodyStyle.fontSize);
            document.getElementById('textTest').textContent = `${fontSize}px`;
            document.getElementById('textTest').className = fontSize >= 16 ? 'good' : fontSize >= 14 ? 'warning' : 'error';
        }

        async function runAPITests() {
            const endpoints = [
                { name: 'Library', url: '/api/library' },
                { name: 'Search', url: '/api/search?q=test' },
                { name: 'Home', url: '/api/home/<USER>' },
                { name: 'Profile', url: '/api/profile' },
                { name: 'Charts', url: '/api/charts' }
            ];

            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing APIs...';

            let results = '';
            for (const endpoint of endpoints) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(`http://localhost:3001${endpoint.url}`);
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);

                    const status = response.ok ? 'good' : 'error';
                    const statusText = response.ok ? `${responseTime}ms` : `Error ${response.status}`;
                    
                    results += `
                        <div class="metric">
                            <span>${endpoint.name} API</span>
                            <span class="metric-value ${status}">${statusText}</span>
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="metric">
                            <span>${endpoint.name} API</span>
                            <span class="metric-value error">Failed</span>
                        </div>
                    `;
                }
            }
            resultsDiv.innerHTML = results;
        }

        function runBrowserTests() {
            // User agent
            const ua = navigator.userAgent;
            const browser = ua.includes('Chrome') ? 'Chrome' : ua.includes('Firefox') ? 'Firefox' : ua.includes('Safari') ? 'Safari' : 'Other';
            document.getElementById('userAgent').textContent = browser;

            // CSS support
            const supportsGrid = CSS.supports('display', 'grid');
            const supportsFlexbox = CSS.supports('display', 'flex');
            const cssScore = (supportsGrid ? 1 : 0) + (supportsFlexbox ? 1 : 0);
            document.getElementById('cssSupport').textContent = `${cssScore}/2 Features`;
            document.getElementById('cssSupport').className = cssScore === 2 ? 'good' : cssScore === 1 ? 'warning' : 'error';

            // JavaScript features
            const hasES6 = typeof Promise !== 'undefined';
            const hasFetch = typeof fetch !== 'undefined';
            const jsScore = (hasES6 ? 1 : 0) + (hasFetch ? 1 : 0);
            document.getElementById('jsFeatures').textContent = `${jsScore}/2 Features`;
            document.getElementById('jsFeatures').className = jsScore === 2 ? 'good' : jsScore === 1 ? 'warning' : 'error';
        }

        async function testAllPages() {
            const pages = [
                'index.html',
                'explore.html',
                'library.html',
                'player.html',
                'profile.html',
                'artist.html',
                'searchresults.html',
                'trendingcharts.html'
            ];

            const resultsDiv = document.getElementById('pageTestResults');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing all pages...';

            let results = '';
            for (const page of pages) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(page);
                    const endTime = performance.now();
                    const loadTime = Math.round(endTime - startTime);

                    const status = response.ok ? (loadTime < 1000 ? 'good' : loadTime < 2000 ? 'warning' : 'error') : 'error';
                    const statusText = response.ok ? `${loadTime}ms` : 'Failed';

                    results += `
                        <div class="page-test">
                            <span>${page}</span>
                            <span class="metric-value ${status}">${statusText}</span>
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="page-test">
                            <span>${page}</span>
                            <span class="metric-value error">Error</span>
                        </div>
                    `;
                }
            }
            resultsDiv.innerHTML = results;
        }

        function generateReport() {
            const recommendations = document.getElementById('recommendations');
            recommendations.innerHTML = `
                <h4>🎯 Performance Recommendations:</h4>
                <ul>
                    <li><strong>Image Optimization:</strong> Convert album covers to WebP format for better compression</li>
                    <li><strong>CSS Minification:</strong> Minify CSS files for production deployment</li>
                    <li><strong>API Caching:</strong> Implement caching for frequently accessed endpoints</li>
                    <li><strong>Lazy Loading:</strong> Add lazy loading for images below the fold</li>
                    <li><strong>Service Worker:</strong> Consider adding offline functionality</li>
                </ul>
                
                <h4>📱 Mobile UX Recommendations:</h4>
                <ul>
                    <li><strong>Touch Gestures:</strong> Add swipe gestures for menu navigation</li>
                    <li><strong>Haptic Feedback:</strong> Implement vibration feedback for touch interactions</li>
                    <li><strong>Pull-to-Refresh:</strong> Add pull-to-refresh on library and explore pages</li>
                    <li><strong>Voice Search:</strong> Consider voice search integration</li>
                </ul>
                
                <h4>🔧 Technical Improvements:</h4>
                <ul>
                    <li><strong>Error Boundaries:</strong> Add better error handling for API failures</li>
                    <li><strong>Loading States:</strong> Improve loading indicators across all pages</li>
                    <li><strong>Accessibility:</strong> Add more ARIA labels and keyboard navigation</li>
                    <li><strong>SEO:</strong> Add meta descriptions and structured data</li>
                </ul>
            `;
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                runCoreTests();
                runBrowserTests();
            }, 1000);
        });
    </script>
</body>
</html>
