// Enhanced Explore Page Management System with Backend Integration
class ExplorePageManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.searchTimeout = null;
        this.isSearching = false;
        this.deezerService = window.deezerService;
        this.init();
    }

    async init() {
        this.bindElements();
        this.bindEvents();
        this.initializeAnimations();

        // Load dynamic content from backend
        await this.loadBackendData();
    }

    bindElements() {
        // Search elements
        this.searchInput = document.getElementById('searchInput');
        this.searchClear = document.getElementById('searchClear');
        this.searchForm = document.querySelector('.search-form');
        this.searchTags = document.querySelectorAll('.search-tag');

        // Genre cards
        this.genreCards = document.querySelectorAll('.genre-card');

        // Hero elements
        this.heroStats = document.querySelectorAll('.stat-number');

        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });

            this.searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch(this.searchInput.value);
                }
            });
        }

        // Search form submission
        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch(this.searchInput.value);
            });
        }

        // Clear search button
        if (this.searchClear) {
            this.searchClear.addEventListener('click', () => {
                this.clearSearch();
            });
        }

        // Quick search tags
        this.searchTags.forEach(tag => {
            tag.addEventListener('click', (e) => {
                const query = e.target.dataset.query;
                this.searchInput.value = query;
                this.performSearch(query);
            });
        });

        // Genre cards
        this.genreCards.forEach(card => {
            card.addEventListener('click', () => {
                const genre = card.dataset.genre;
                this.exploreGenre(genre);
            });

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    const genre = card.dataset.genre;
                    this.exploreGenre(genre);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.searchInput.focus();
            }
        });
    }

    initializeAnimations() {
        // Animate hero stats on page load
        this.animateHeroStats();

        // Add intersection observer for genre cards
        this.observeGenreCards();
    }

    handleSearchInput(value) {
        // Show/hide clear button
        if (value.trim()) {
            this.searchClear.classList.remove('hidden');
        } else {
            this.searchClear.classList.add('hidden');
        }

        // Debounce search suggestions
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(() => {
            if (value.trim().length >= 2) {
                this.showSearchSuggestions(value.trim());
            }
        }, 300);
    }

    performSearch(query) {
        if (!query.trim()) {
            this.announceAction('Please enter a search term');
            return;
        }

        this.isSearching = true;
        this.announceAction(`Searching for "${query}"`);

        // Simulate search by redirecting to search results page
        const searchUrl = `searchresults.html?q=${encodeURIComponent(query)}`;
        window.location.href = searchUrl;
    }

    clearSearch() {
        this.searchInput.value = '';
        this.searchClear.classList.add('hidden');
        this.searchInput.focus();
        this.announceAction('Search cleared');
    }

    async showSearchSuggestions(query) {
        try {
            console.log(`🎵 Getting Deezer suggestions for: ${query}`);
            const suggestions = await this.deezerService.search(query, 'track', 5);

            if (suggestions && suggestions.data && suggestions.data.length > 0) {
                this.displaySearchSuggestions(suggestions.data, query);
            }
        } catch (error) {
            console.error('❌ Failed to get Deezer suggestions:', error);
        }
    }

    displaySearchSuggestions(suggestions, query) {
        // Remove existing suggestions
        const existingSuggestions = document.querySelector('.search-suggestions');
        if (existingSuggestions) {
            existingSuggestions.remove();
        }

        // Create suggestions dropdown
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'search-suggestions';
        suggestionsContainer.innerHTML = `
            <div class="suggestions-header">
                <span>Suggestions from Deezer</span>
            </div>
            <div class="suggestions-list">
                ${suggestions.map(track => `
                    <div class="suggestion-item" data-query="${track.title} ${track.artist}">
                        <img src="${track.cover || 'imgs/album-01.png'}" alt="${track.title}" class="suggestion-image">
                        <div class="suggestion-info">
                            <div class="suggestion-title">${track.title}</div>
                            <div class="suggestion-artist">${track.artist}</div>
                        </div>
                        <button class="suggestion-play" data-preview="${track.preview}" data-title="${track.title}" data-artist="${track.artist}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                `).join('')}
            </div>
        `;

        // Insert suggestions below search input
        const searchForm = document.querySelector('.search-form');
        searchForm.appendChild(suggestionsContainer);

        // Bind suggestion click events
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.suggestion-play')) {
                    const query = item.dataset.query;
                    this.searchInput.value = query;
                    this.performSearch(query);
                }
            });
        });

        // Auto-hide suggestions after 10 seconds
        setTimeout(() => {
            if (suggestionsContainer.parentNode) {
                suggestionsContainer.remove();
            }
        }, 10000);
    }

    async exploreGenre(genre) {
        this.announceAction(`Exploring ${genre} music`);

        try {
            console.log(`🎵 Loading ${genre} tracks from Deezer...`);

            // Search for genre-specific tracks
            const genreData = await this.deezerService.search(genre, 'track', 20);

            if (genreData && genreData.data && genreData.data.length > 0) {
                // Store genre data for search results page
                sessionStorage.setItem('genreData', JSON.stringify({
                    genre,
                    tracks: genreData.data,
                    source: 'deezer'
                }));
            }
        } catch (error) {
            console.error(`❌ Failed to load ${genre} data from Deezer:`, error);
        }

        // Navigate to search results page
        const genreUrl = `searchresults.html?genre=${encodeURIComponent(genre)}`;
        window.location.href = genreUrl;
    }

    animateHeroStats() {
        this.heroStats.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');

            // Animate from 0 to final value
            let currentValue = 0;
            const increment = numericValue / 50;
            const duration = 2000; // 2 seconds
            const stepTime = duration / 50;

            setTimeout(() => {
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= numericValue) {
                        currentValue = numericValue;
                        clearInterval(timer);
                    }

                    stat.textContent = currentValue.toFixed(1) + suffix;
                }, stepTime);
            }, index * 200); // Stagger animations
        });
    }

    observeGenreCards() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        this.genreCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    announceAction(message) {
        // Use shared utility
        window.bansheeUtils.announceAction(message);
    }

    // ===== BACKEND INTEGRATION METHODS =====

    async loadBackendData() {
        try {
            console.log('🎵 Loading Explore page data from backend...');

            // Load all explore data in parallel using shared utility
            const endpoints = [
                { url: '/explore', fallback: this.fallbackData.explore },
                { url: '/explore/genres', fallback: this.fallbackData.genres }
            ];

            const results = await window.bansheeUtils.fetchMultiple(endpoints);
            const [exploreResponse, genresResponse] = results.map(result => ({
                ok: result.status === 'fulfilled',
                json: () => Promise.resolve(result.value || result.reason)
            }));

            // Process responses
            if (exploreResponse.ok) {
                const exploreData = await exploreResponse.json();
                this.updateHeroStats(exploreData.stats);
                this.renderFeaturedContent(exploreData);
            }

            if (genresResponse.ok) {
                const genres = await genresResponse.json();
                this.updateGenreCards(genres);
            }

            console.log('✅ Explore data loaded successfully!');
            this.announceAction('Explore page loaded with fresh content!');

        } catch (error) {
            console.error('❌ Error loading backend data:', error);
            this.announceAction('Using demo content - server unavailable');
        }
    }

    updateHeroStats(stats) {
        const statNumbers = document.querySelectorAll('.hero-stats .stat-number');
        if (statNumbers.length >= 3) {
            this.animateStatNumber(statNumbers[0], stats.totalTracks);
            this.animateStatNumber(statNumbers[1], stats.totalArtists);
            this.animateStatNumber(statNumbers[2], stats.totalAlbums);
        }
        console.log('📊 Hero stats updated:', stats);
    }

    animateStatNumber(element, targetValue) {
        // Extract number from string like "50M+" -> 50
        const numericValue = parseFloat(targetValue.toString().replace(/[^\d.]/g, '')) || 0;
        const suffix = targetValue.toString().replace(/[\d.]/g, '');

        const startValue = 0;
        const duration = 2000; // 2 seconds
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = (startValue + (numericValue - startValue) * easeOutQuart).toFixed(1);

            element.textContent = currentValue + suffix;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    updateGenreCards(genres) {
        const genreCards = document.querySelectorAll('.genre-card');

        genres.slice(0, genreCards.length).forEach((genre, index) => {
            const card = genreCards[index];
            if (card) {
                // Update genre stats
                const statsElement = card.querySelector('.genre-stats span');
                if (statsElement) {
                    statsElement.innerHTML = `<i class="fas fa-music"></i> ${genre.trackCount} tracks`;
                }

                // Update genre description
                const descElement = card.querySelector('p');
                if (descElement) {
                    descElement.textContent = genre.description;
                }
            }
        });

        console.log(`✅ Updated ${genres.length} genre cards`);
    }

    renderFeaturedContent(exploreData) {
        // Update featured artists carousel
        this.renderFeaturedArtists(exploreData.featuredArtists);
        console.log('✅ Featured content rendered');
    }

    renderFeaturedArtists(artists) {
        const carouselTrack = document.querySelector('.featured-artists .carousel-track');
        if (!carouselTrack || !artists.length) return;

        carouselTrack.innerHTML = artists.map(artist => `
            <div class="carousel-card">
                <div class="card">
                    <div class="img-container">
                        <img src="${artist.image}" alt="Featured Artist: ${artist.name}" loading="lazy" />
                        <div class="play-overlay">
                            <button type="button" class="play-button" aria-label="Play ${artist.name}">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${artist.name}</h3>
                            <p>${artist.genre} • ${artist.followers} followers</p>
                        </div>
                        <a href="artist.html?artist=${encodeURIComponent(artist.name)}" class="button">View Artist</a>
                    </div>
                </div>
            </div>
        `).join('');

        this.bindFeaturedArtistEvents(carouselTrack);
    }

    bindFeaturedArtistEvents(container) {
        // Play button events
        container.querySelectorAll('.play-button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const artistName = btn.closest('.card').querySelector('h3').textContent;
                this.playArtist(artistName);
            });
        });
    }

    async playArtist(artistName) {
        this.announceAction(`Playing ${artistName}`);
        console.log(`🎵 Playing artist: ${artistName}`);

        try {
            // Search for tracks by this artist
            const artistData = await this.deezerService.search(artistName, 'track', 1);

            if (artistData && artistData.data && artistData.data.length > 0) {
                const track = artistData.data[0];
                const trackData = {
                    preview: track.preview,
                    title: track.title,
                    artist: track.artist?.name || artistName,
                    artwork: track.album?.cover_medium || 'imgs/album-01.png'
                };

                // Use the mini player if available
                if (window.exploreMiniPlayer && window.exploreMiniPlayer.playTrack) {
                    window.exploreMiniPlayer.playTrack(trackData);
                }
            }
        } catch (error) {
            console.error(`❌ Failed to play artist ${artistName}:`, error);
        }
    }
}

// Enhanced Carousel Management (if not handled by main.js)
class EnhancedCarousel {
    constructor(container) {
        this.container = container;
        this.track = container.querySelector('.carousel-track');
        this.cards = container.querySelectorAll('.carousel-card');
        this.prevBtn = container.querySelector('.carousel-button.prev');
        this.nextBtn = container.querySelector('.carousel-button.next');
        this.currentIndex = 0;
        this.cardWidth = 280;
        this.gap = 30;
        this.visibleCards = this.calculateVisibleCards();

        this.init();
    }

    init() {
        this.bindEvents();
        this.updateButtons();
        this.setupAutoPlay();
    }

    calculateVisibleCards() {
        const containerWidth = this.container.offsetWidth;
        return Math.floor((containerWidth - 120) / (this.cardWidth + this.gap));
    }

    bindEvents() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prev());
        }

        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.next());
        }

        // Touch/swipe support
        let startX = 0;
        let currentX = 0;
        let isDragging = false;

        this.track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            isDragging = true;
        });

        this.track.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            currentX = e.touches[0].clientX;
            const diff = startX - currentX;

            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    this.next();
                } else {
                    this.prev();
                }
                isDragging = false;
            }
        });

        this.track.addEventListener('touchend', () => {
            isDragging = false;
        });

        // Keyboard navigation
        this.container.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                this.prev();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                this.next();
            }
        });

        // Resize handler
        window.addEventListener('resize', () => {
            this.visibleCards = this.calculateVisibleCards();
            this.updateButtons();
        });
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.updateCarousel();
        }
    }

    next() {
        const maxIndex = Math.max(0, this.cards.length - this.visibleCards);
        if (this.currentIndex < maxIndex) {
            this.currentIndex++;
            this.updateCarousel();
        }
    }

    updateCarousel() {
        const translateX = -(this.currentIndex * (this.cardWidth + this.gap));
        this.track.style.transform = `translateX(${translateX}px)`;
        this.updateButtons();
    }

    updateButtons() {
        const maxIndex = Math.max(0, this.cards.length - this.visibleCards);

        if (this.prevBtn) {
            this.prevBtn.disabled = this.currentIndex === 0;
            this.prevBtn.style.opacity = this.currentIndex === 0 ? '0.5' : '1';
        }

        if (this.nextBtn) {
            this.nextBtn.disabled = this.currentIndex >= maxIndex;
            this.nextBtn.style.opacity = this.currentIndex >= maxIndex ? '0.5' : '1';
        }
    }

    setupAutoPlay() {
        // Optional auto-play functionality
        setInterval(() => {
            const maxIndex = Math.max(0, this.cards.length - this.visibleCards);
            if (this.currentIndex >= maxIndex) {
                this.currentIndex = 0;
            } else {
                this.currentIndex++;
            }
            this.updateCarousel();
        }, 5000); // Auto-advance every 5 seconds
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize explore page manager
    window.exploreManager = new ExplorePageManager();

    // Initialize enhanced carousels
    const carousels = document.querySelectorAll('.carousel-container');
    carousels.forEach(carousel => {
        new EnhancedCarousel(carousel);
    });
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ExplorePageManager, EnhancedCarousel };
}