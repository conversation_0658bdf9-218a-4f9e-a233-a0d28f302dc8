<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banshee Performance Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #ff006e;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #1a5d1a; }
        .warning { background: #5d5d1a; }
        .error { background: #5d1a1a; }
        button {
            background: #ff006e;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #e6005f; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-card {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #00e0ff;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #333;
            border-radius: 50%;
            border-top-color: #ff006e;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>🎵 Banshee Performance Testing Dashboard</h1>
    
    <div class="test-section">
        <h2>📊 Performance Metrics</h2>
        <div class="metrics" id="metrics">
            <div class="metric-card">
                <div class="metric-value" id="loadTime">-</div>
                <div>Page Load Time (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="apiResponse">-</div>
                <div>API Response (ms)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="domNodes">-</div>
                <div>DOM Nodes</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="memoryUsage">-</div>
                <div>Memory Usage (MB)</div>
            </div>
        </div>
        <button onclick="runPerformanceTests()">🚀 Run Performance Tests</button>
        <button onclick="testAllPages()">📱 Test All Pages</button>
    </div>

    <div class="test-section">
        <h2>🔗 API Endpoint Testing</h2>
        <div id="apiResults"></div>
        <button onclick="testAPIEndpoints()">🧪 Test API Endpoints</button>
    </div>

    <div class="test-section">
        <h2>📱 Responsive Design Testing</h2>
        <div id="responsiveResults"></div>
        <button onclick="testResponsiveDesign()">📐 Test Breakpoints</button>
    </div>

    <div class="test-section">
        <h2>⚡ Quick Page Tests</h2>
        <div id="pageResults"></div>
        <button onclick="testPageLoad('index.html')">🏠 Test Home</button>
        <button onclick="testPageLoad('library.html')">📚 Test Library</button>
        <button onclick="testPageLoad('explore.html')">🔍 Test Explore</button>
        <button onclick="testPageLoad('player.html')">▶️ Test Player</button>
    </div>

    <script>
        // Performance testing functions
        function runPerformanceTests() {
            // Page load time
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            document.getElementById('loadTime').textContent = loadTime;

            // DOM nodes count
            const domNodes = document.getElementsByTagName('*').length;
            document.getElementById('domNodes').textContent = domNodes;

            // Memory usage (if available)
            if (performance.memory) {
                const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1048576);
                document.getElementById('memoryUsage').textContent = memoryMB;
            }

            // Test API response time
            testAPIResponse();
        }

        async function testAPIResponse() {
            const startTime = performance.now();
            try {
                const response = await fetch('http://localhost:3001/api/home/<USER>');
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                document.getElementById('apiResponse').textContent = responseTime;
                
                if (responseTime > 500) {
                    addResult('apiResults', `⚠️ API response slow: ${responseTime}ms`, 'warning');
                } else {
                    addResult('apiResults', `✅ API response good: ${responseTime}ms`, 'success');
                }
            } catch (error) {
                document.getElementById('apiResponse').textContent = 'Error';
                addResult('apiResults', `❌ API Error: ${error.message}`, 'error');
            }
        }

        async function testAPIEndpoints() {
            const endpoints = [
                '/api/library',
                '/api/home/<USER>',
                '/api/search?q=test',
                '/api/tracks',
                '/api/profile',
                '/api/charts'
            ];

            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing API endpoints...';

            for (const endpoint of endpoints) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(`http://localhost:3001${endpoint}`);
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);

                    if (response.ok) {
                        addResult('apiResults', `✅ ${endpoint}: ${responseTime}ms`, 'success');
                    } else {
                        addResult('apiResults', `❌ ${endpoint}: ${response.status}`, 'error');
                    }
                } catch (error) {
                    addResult('apiResults', `❌ ${endpoint}: ${error.message}`, 'error');
                }
            }
        }

        function testResponsiveDesign() {
            const breakpoints = [
                { name: 'Mobile Small', width: 320 },
                { name: 'Mobile Large', width: 768 },
                { name: 'Tablet', width: 1024 },
                { name: 'Desktop', width: 1440 }
            ];

            const resultsDiv = document.getElementById('responsiveResults');
            resultsDiv.innerHTML = '';

            breakpoints.forEach(bp => {
                // Simulate viewport change (for demonstration)
                const mediaQuery = window.matchMedia(`(max-width: ${bp.width}px)`);
                const status = mediaQuery.matches ? '📱 Active' : '💻 Inactive';
                addResult('responsiveResults', `${bp.name} (${bp.width}px): ${status}`, 'success');
            });

            addResult('responsiveResults', `Current viewport: ${window.innerWidth}x${window.innerHeight}`, 'success');
        }

        async function testPageLoad(page) {
            const startTime = performance.now();
            try {
                // Create a hidden iframe to test page load
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = page;
                
                iframe.onload = () => {
                    const endTime = performance.now();
                    const loadTime = Math.round(endTime - startTime);
                    addResult('pageResults', `✅ ${page}: ${loadTime}ms`, loadTime > 2000 ? 'warning' : 'success');
                    document.body.removeChild(iframe);
                };

                iframe.onerror = () => {
                    addResult('pageResults', `❌ ${page}: Failed to load`, 'error');
                    document.body.removeChild(iframe);
                };

                document.body.appendChild(iframe);
            } catch (error) {
                addResult('pageResults', `❌ ${page}: ${error.message}`, 'error');
            }
        }

        async function testAllPages() {
            const pages = [
                'index.html',
                'library.html',
                'explore.html',
                'player.html',
                'profile.html',
                'artist.html',
                'searchresults.html',
                'trendingcharts.html'
            ];

            const resultsDiv = document.getElementById('pageResults');
            resultsDiv.innerHTML = '<div class="loading"></div> Testing all pages...';

            for (const page of pages) {
                await new Promise(resolve => {
                    setTimeout(() => {
                        testPageLoad(page);
                        resolve();
                    }, 500);
                });
            }
        }

        function addResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(runPerformanceTests, 1000);
        });
    </script>
</body>
</html>
