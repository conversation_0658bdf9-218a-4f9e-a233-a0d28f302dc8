<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Minimal Audio Test</title>
  <style>
    #miniPlayerTest {
      position: fixed;
      bottom: 20px;
      left: 20px;
      background: #222;
      color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px #0008;
      z-index: 9999;
    }
    #miniPlayerTest button {
      margin-right: 10px;
    }
  </style>
</head>
<body>
  <div id="miniPlayerTest">
    <button id="playTestBtn">Play Test Audio</button>
    <span id="audioStatus">Idle</span>
    <audio id="audioTest" preload="none"></audio>
  </div>
  <script>
    // Known-good public MP3 file
    const previewUrl = "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3";
    const playBtn = document.getElementById('playTestBtn');
    const audio = document.getElementById('audioTest');
    const status = document.getElementById('audioStatus');

    playBtn.addEventListener('click', () => {
      audio.src = previewUrl;
      audio.currentTime = 0;
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          status.textContent = 'Playing...';
        }).catch((err) => {
          status.textContent = 'Error: ' + err.message;
        });
      } else {
        status.textContent = 'Playing...';
      }
    });
    audio.addEventListener('ended', () => {
      status.textContent = 'Ended';
    });
    audio.addEventListener('pause', () => {
      if (!audio.ended) status.textContent = 'Paused';
    });
    audio.addEventListener('play', () => {
      status.textContent = 'Playing...';
    });
    audio.addEventListener('error', () => {
      status.textContent = 'Error: Could not play audio.';
    });
  </script>
</body>
</html>
