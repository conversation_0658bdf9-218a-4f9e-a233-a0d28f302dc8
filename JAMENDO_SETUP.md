# Jamendo API Integration Setup Guide

## Overview
Banshee Music now includes integration with Jamendo's free music API, providing access to 600,000+ Creative Commons licensed tracks that can be legally streamed and downloaded.

## Benefits
- **Real Playable Music**: Unlike <PERSON><PERSON> (metadata only), Jamendo provides actual audio URLs
- **No Licensing Issues**: All music is Creative Commons licensed
- **Free for Non-Commercial Use**: 35,000 API requests per month
- **High Quality**: MP3 VBR and FLAC formats available
- **Rich Metadata**: Genre tags, artist info, album art, and more

## Setup Instructions

### 1. Get Your Jamendo API Key
1. Visit [devportal.jamendo.com](https://devportal.jamendo.com)
2. Sign up for a free account
3. Create a new application
4. Copy your Client ID

### 2. Configure Banshee Music
1. Open `js/services/jamendo-service.js`
2. Find the line: `this.clientId = 'YOUR_JAMENDO_CLIENT_ID';`
3. Replace `YOUR_JAMENDO_CLIENT_ID` with your actual Client ID
4. Save the file

### 3. Test the Integration
1. Open your browser's developer console (F12)
2. Navigate to any page in Banshee Music
3. Look for console messages starting with "🎵 Jamendo"
4. You should see successful API calls and music loading

## How It Works

### Hybrid Music Service
Banshee now uses a "hybrid" approach:
- **Jamendo**: Provides playable music content (preferred)
- **Deezer**: Provides rich metadata and fallback content
- **Smart Fallbacks**: If one service fails, the other takes over

### Pages with Jamendo Integration
- **Home Page**: Featured tracks, trending music, new releases
- **Search Results**: Real playable search results
- **Library Page**: Playlists and saved music
- **Trending Charts**: Popular tracks with actual audio
- **Music Player**: Plays real Jamendo tracks

### Audio Quality Options
- `mp31`: 96kbps MP3 (default for streaming)
- `mp32`: VBR MP3 (good quality, used for downloads)
- `ogg`: Ogg Vorbis format
- `flac`: Lossless FLAC format

## API Limits
- **Free Tier**: 35,000 requests/month
- **Rate Limiting**: 200ms between requests (built-in)
- **Caching**: 5-minute cache to reduce API calls
- **Efficient Usage**: Batch requests and smart fallbacks

## Troubleshooting

### Common Issues
1. **"Application Suspended" Error**
   - Check your Client ID is correct
   - Ensure your Jamendo account is active
   - Verify you haven't exceeded rate limits

2. **No Music Playing**
   - Check browser console for errors
   - Verify CORS isn't blocking requests
   - Test with a simple API call

3. **Empty Search Results**
   - Jamendo has different content than Spotify/Apple Music
   - Try broader search terms
   - Check if the genre/artist exists in Jamendo's catalog

### Debug Commands
Open browser console and try:
```javascript
// Test Jamendo service
await window.jamendoService.healthCheck();

// Test hybrid service
await window.hybridMusicService.init();

// Search for music
await window.hybridMusicService.search('rock', 'track', 5);

// Get featured tracks
await window.jamendoService.getFeaturedTracks(5);
```

## Content Guidelines
- All Jamendo music is Creative Commons licensed
- Most tracks allow commercial use with attribution
- Some tracks may have specific license restrictions
- Always check the `license` field in track data

## Performance Tips
1. **Use Caching**: The service automatically caches responses
2. **Batch Requests**: Search for multiple items at once when possible
3. **Optimize Limits**: Use appropriate limit values (don't request 200 tracks if you only show 10)
4. **Monitor Usage**: Keep track of your API usage in the Jamendo developer portal

## Future Enhancements
- Playlist creation with Jamendo tracks
- User favorites and library sync
- Advanced search filters (tempo, mood, instruments)
- Integration with Jamendo's radio streams
- Support for Jamendo Pro licensing features

## Support
- **Jamendo API Docs**: [developer.jamendo.com](https://developer.jamendo.com/v3.0)
- **Jamendo Community**: [Google Group](https://groups.google.com/forum/#!forum/jamendo-dev)
- **Banshee Issues**: Check browser console for detailed error messages

---

**Note**: This integration transforms Banshee from a UI demo into a functional music streaming application with real, legally licensed content!
