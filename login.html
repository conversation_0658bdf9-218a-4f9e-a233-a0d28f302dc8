<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/login.css">
    <title>Document</title>
</head>



<body>

    <h1 class="welcome-header">Welcome to Banshee Music</h1>
    <div class="login-container">
        <div class="logo">
            <img src="imgs/logo-B.png" alt="Banshee Logo">
        </div>
        <h1>Login to Banshee</h1>
        <form id="loginForm" method="POST" action="/api/auth/login" autocomplete="on" novalidate class="login-form">
            <input type="hidden" name="csrf_token" id="csrf_token">
            <div class="input-group">
                <input type="text" id="username" name="username" required
                       minlength="3" maxlength="50"
                       pattern="^[a-zA-Z0-9_-]+$"
                       autocomplete="username"
                       placeholder="Username">
            </div>
            <div class="input-group">
                <input type="password" id="password" name="password" required
                       minlength="8" maxlength="128"
                       autocomplete="current-password"
                       placeholder="Password">
            </div>
            <!-- CAPTCHA Challenge -->
            <div id="captchaContainer" class="captcha-container">
                <div class="captcha-question" id="captchaQuestion">What is 5 + 3?</div>
                <div class="captcha-input-group">
                    <input type="text" id="captchaAnswer" name="captchaAnswer"
                           placeholder="Enter answer" required>
                    <button type="button" id="refreshCaptcha" class="refresh-captcha" aria-label="Refresh CAPTCHA">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M23 4v6h-6"/>
                            <path d="M1 20v-6h6"/>
                            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/>
                        </svg>
                    </button>
                </div>
                <div class="captcha-error" id="captchaError"></div>
            </div>

            <div class="remember-forgot">
                <label class="remember-me">
                    <input type="checkbox" name="remember"> Remember me
                </label>
                <a href="#" class="forgot-password">Forgot Password?</a>
            </div>
            <button type="submit" class="login-button">Login</button>
            <div class="signup-link">
                Don't have an account? <a href="signup.html">Sign up</a>
            </div>
        </form>
    </div>
    <div id="message" class="message"></div>
    <div id="loader" class="loader-container">
        <div class="loader"></div>
    </div>

    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>

            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>

            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/login.js"></script>

    <!-- Login Page Mini Player Integration -->
    <script>
        // Login Page Mini Player Integration
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for global mini player to be initialized
            setTimeout(() => {
                if (window.miniPlayer) {
                    console.log('🎵 Login page mini player integration ready');
                    // Note: Login page typically won't have play buttons
                    // But mini player is available if needed
                } else {
                    console.warn('⚠️ Global mini player not available');
                }
            }, 100);
        });
    </script>
</body>
</html>