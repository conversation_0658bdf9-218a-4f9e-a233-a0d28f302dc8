/* Search Results page specific styles - CSS variables inherited from styles.css */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: var(--background-primary);
    color: var(--text-primary);
}

/* Navbar Styles */
header {
    background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */
.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative;
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease-out;
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1);
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px;
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: var(--header-gradient-start);
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px;
    background: transparent;
}

.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    z-index: 0;
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
    border-radius: inherit;
}

.dropdown a:hover {
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1;
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    left: -999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--neon-blue);
    color: #fff;
    z-index: 3000;
    padding: 0.5em 1em;
    border-radius: 8px;
    font-weight: 700;
    transition: left 0.2s;
}

.skip-link:focus {
    left: 10px;
    top: 10px;
    width: auto;
    height: auto;
    outline: 2px solid var(--cosmic-pink);
}

/* Main container styles */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 120px 20px 40px;
    min-height: 100vh;
}

/* Search results page layout */
.search-results-page {
    width: 100%;
}

/* Search Header */
.search-header {
    margin-bottom: 2rem;
    position: sticky;
    top: 100px;
    z-index: 100;
    background: var(--background-primary);
    padding: 1rem 0;
    border-radius: 16px;
}

.search-bar-container {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 1rem 3rem 1rem 3rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    outline: none;
}

.search-input:focus {
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 4px rgba(0, 224, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.search-input::placeholder {
    color: var(--text-secondary);
}

.clear-search-btn {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-fast);
    opacity: 0;
    visibility: hidden;
}

.search-input:not(:placeholder-shown) + .clear-search-btn {
    opacity: 1;
    visibility: visible;
}

.clear-search-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.search-filters-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 1rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.search-filters-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--neon-blue);
}

.search-filters-btn.active {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
}

/* Search Filters Panel */
.search-filters-panel {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    margin-top: 1rem;
    transition: all var(--transition-normal);
}

.filters-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.filter-group select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.75rem;
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all var(--transition-normal);
}

.filter-group select:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 3px rgba(0, 224, 255, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    grid-column: 1 / -1;
    margin-top: 0.5rem;
}

.filter-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.apply-filters-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
}

.apply-filters-btn:hover {
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.3);
}

/* Search Query Info */
.search-query-info {
    text-align: center;
    margin-bottom: 2rem;
}

.search-title-gradient {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    letter-spacing: 1.5px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.search-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Quick Suggestions */
.quick-suggestions {
    margin-bottom: 3rem;
}

.quick-suggestions h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    max-width: 800px;
    margin: 0 auto;
}

.suggestion-tag {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    font-size: 0.9rem;
}

.suggestion-tag:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--neon-blue);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Search Results Sections */
.search-results-sections {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.results-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.view-all-btn {
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    font-size: 0.9rem;
}

.view-all-btn:hover {
    color: var(--text-primary);
    border-color: var(--neon-blue);
    background: rgba(0, 224, 255, 0.1);
}

/* Top Result */
.top-result-section {
    background: linear-gradient(135deg, rgba(0, 224, 255, 0.1), rgba(255, 0, 110, 0.1));
    border: 1px solid rgba(0, 224, 255, 0.2);
}

.top-result-card {
    display: flex;
    align-items: center;
    gap: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 2rem;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.top-result-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.top-result-image {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.top-result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.top-result-info {
    flex: 1;
}

.top-result-type {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.top-result-title {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.top-result-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.top-result-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.play-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all var(--transition-normal);
}

.play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Results Grids */
.results-grid {
    display: grid;
    gap: 1.5rem;
}

.songs-grid {
    grid-template-columns: 1fr;
}

.artists-grid,
.albums-grid,
.playlists-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* Song Result Card */
.song-result-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1rem;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.song-result-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.song-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.song-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.song-info {
    flex: 1;
    min-width: 0;
}

.song-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-artist {
    color: var(--text-secondary);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-duration {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    flex-shrink: 0;
}

.song-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.song-result-card:hover .song-actions {
    opacity: 1;
}

.song-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all var(--transition-fast);
    font-size: 1rem;
}

.song-action-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.song-action-btn.like-btn.liked {
    color: var(--cosmic-pink);
    background: rgba(255, 0, 110, 0.1);
}

.song-action-btn.like-btn.liked:hover {
    color: var(--cosmic-pink);
    background: rgba(255, 0, 110, 0.2);
}

/* Artist/Album/Playlist Result Cards */
.result-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.result-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.result-image {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
    position: relative;
}

.result-image.artist {
    border-radius: 50%;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.result-image .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.result-card:hover .play-overlay {
    opacity: 1;
}

.result-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    text-align: center;
    line-height: 1.3;
}

.result-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
    text-align: center;
    margin-bottom: 1rem;
}

.result-meta {
    color: var(--text-secondary);
    font-size: 0.8rem;
    text-align: center;
}

/* No Results State */
.no-results-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.no-results-state h3 {
    color: var(--text-primary);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.no-results-state > p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.no-results-suggestions {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1.5rem;
    max-width: 400px;
    margin: 0 auto;
    text-align: left;
}

.no-results-suggestions h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.no-results-suggestions ul {
    list-style: none;
    padding: 0;
}

.no-results-suggestions li {
    color: var(--text-secondary);
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
    padding-left: 1.5rem;
}

.no-results-suggestions li:last-child {
    border-bottom: none;
}

.no-results-suggestions li::before {
    content: '•';
    color: var(--neon-blue);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.loading-spinner {
    margin-bottom: 2rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid var(--neon-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-state p {
    font-size: 1.1rem;
    color: var(--text-primary);
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .search-bar-container {
        flex-direction: column;
        gap: 1rem;
    }

    .search-filters-btn {
        align-self: stretch;
        justify-content: center;
    }

    .filters-content {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .top-result-card {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .top-result-title {
        font-size: 1.5rem;
    }

    .artists-grid,
    .albums-grid,
    .playlists-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .search-header {
        top: 85px;
        padding: 0.5rem 0;
    }

    .search-input {
        padding: 0.75rem 2.5rem 0.75rem 2.5rem;
        font-size: 1rem;
    }

    .search-title-gradient {
        font-size: 2rem;
    }

    .results-section {
        padding: 1.5rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .song-result-card {
        padding: 0.75rem;
    }

    .song-image {
        width: 50px;
        height: 50px;
    }

    .result-image {
        width: 100px;
        height: 100px;
    }

    .filter-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .search-title-gradient {
        font-size: 1.8rem;
    }

    .suggestion-tags {
        gap: 0.5rem;
    }

    .suggestion-tag {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .results-section {
        padding: 1rem;
    }

    .artists-grid,
    .albums-grid,
    .playlists-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
    }

    .result-card {
        padding: 1rem;
    }

    .result-image {
        width: 80px;
        height: 80px;
    }

    .top-result-image {
        width: 100px;
        height: 100px;
    }

    .top-result-title {
        font-size: 1.3rem;
    }

    .song-info {
        min-width: 0;
    }

    .song-title,
    .song-artist {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Focus styles for accessibility */
.search-input:focus,
.search-filters-btn:focus,
.filter-btn:focus,
.suggestion-tag:focus,
.view-all-btn:focus,
.play-btn:focus,
.action-btn:focus,
.song-action-btn:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .results-section,
    .search-filters-panel,
    .result-card,
    .song-result-card {
        border: 2px solid var(--text-primary);
    }

    .search-input {
        border: 2px solid var(--text-primary);
    }

    .play-btn,
    .apply-filters-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }
}