<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/signup.css">
    <title>Sign Up | Banshee Music</title>
</head>



<body>

    <h1 class="welcome-header">Join Banshee Music</h1>
    <div class="signup-container">
        <div class="logo">
            <img src="imgs/logo-B.png" alt="Banshee Logo">
        </div>
        <h1>Create Your Account</h1>
        <p class="email-verification-notice">After signing up, you’ll receive an email to verify your account.</p>
        <form id="signupForm" method="POST" action="/api/auth/signup" autocomplete="on" novalidate>
            <div id="error-summary" class="error-summary" aria-live="assertive" style="display:none;"></div>
            <input type="hidden" name="csrf_token" id="csrf_token">
            <div class="input-group profile-picture-group">
                <label for="profile-picture">Profile Picture (optional)</label>
                <div class="profile-picture-preview">
                    <img id="profile-picture-img" src="imgs/profile-icon-B.png" alt="Profile Picture" width="80" height="80">
                </div>
                <input type="file" id="profile-picture" name="profile-picture" accept="image/*">
            </div>
            <div class="input-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required aria-required="true"
                       minlength="3" maxlength="50"
                       pattern="^[a-zA-Z0-9_-]+$"
                       autocomplete="username">
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="input-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required aria-required="true"
                       pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                       autocomplete="email">
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="input-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required aria-required="true"
                       minlength="8" maxlength="128"
                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
                       autocomplete="new-password">
                <i class="fas fa-eye password-toggle" aria-label="Toggle password visibility"></i>
                <div class="password-requirements-tooltip">
                    <strong>Password must contain:</strong>
                    <ul id="password-rules">
                        <li data-rule="length">At least 8 characters</li>
                        <li data-rule="uppercase">One uppercase letter</li>
                        <li data-rule="lowercase">One lowercase letter</li>
                        <li data-rule="number">One number</li>
                        <li data-rule="special">One special character (@$!%*?&)</li>
                    </ul>
                </div>
                <div class="password-strength" aria-live="polite">
                    <div class="strength-bar weak"></div>
                    <div class="strength-bar medium"></div>
                    <div class="strength-bar strong"></div>
                </div>
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="input-group">
                <label for="confirm-password">Confirm Password</label>
                <input type="password" id="confirm-password" name="confirm-password" required aria-required="true"
                       minlength="8" maxlength="128"
                       autocomplete="new-password">
                <i class="fas fa-eye password-toggle" aria-label="Toggle password visibility"></i>
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="terms-group">
                <label>
                    <input type="checkbox" required aria-required="true">
                    I agree to the <a href="#" id="terms-link" target="_blank" rel="noopener">Terms of Service</a> and <a href="#" id="privacy-link" target="_blank" rel="noopener">Privacy Policy</a>
                </label>
            </div>
            <div class="newsletter-group">
                <label>
                    <input type="checkbox" id="newsletter-optin" name="newsletter-optin">
                    Sign me up for the Banshee Music newsletter (optional)
                </label>
            </div>
            <button type="submit" class="signup-button">Create Account</button>
        </form>
        <div class="social-signup">
            <p>Or sign up with</p>
            <div class="social-buttons" role="group" aria-label="Social signup options">
                <button class="social-button" aria-label="Sign up with Google" data-tooltip="Coming soon!">
                    <i class="fab fa-google"></i>
                    Google
                </button>
                <button class="social-button" aria-label="Sign up with Facebook" data-tooltip="Coming soon!">
                    <i class="fab fa-facebook-f"></i>
                    Facebook
                </button>
                <button class="social-button" aria-label="Sign up with Apple" data-tooltip="Coming soon!">
                    <i class="fab fa-apple"></i>
                    Apple
                </button>
            </div>
        </div>
        <p class="login-link">Already have an account? <a href="login.html">Login here</a></p>
    </div>
    <div id="message" class="message"></div>
    <div id="loader" class="loader-container">
        <div class="loader"></div>
    </div>
    <!-- Terms Modal -->
    <div id="terms-modal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close-modal" id="close-terms">&times;</span>
            <h2>Terms of Service</h2>
            <p>Short summary of the terms goes here. (Full terms would be linked or shown on a separate page.)</p>
        </div>
    </div>
    <!-- Privacy Modal -->
    <div id="privacy-modal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close-modal" id="close-privacy">&times;</span>
            <h2>Privacy Policy</h2>
            <p>Short summary of the privacy policy goes here. (Full policy would be linked or shown on a separate page.)</p>
        </div>
    </div>
    <!-- Email Verification Modal -->
    <div id="verify-modal" class="modal" style="display:none;">
        <div class="modal-content">
            <h2>Verify Your Email</h2>
            <p>We’ve sent a verification link to your email. Please check your inbox and click the link to activate your account.</p>
            <button id="resend-verification" class="signup-button">Resend Email</button>
        </div>
    </div>


    <script src="js/signup.js"></script>
    
</body>
</html>