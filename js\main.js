class Carousel {
    constructor(container) {
        if (!container) {
            throw new Error('Carousel container element is required');
        }

        this.container = container;
        this.track = container.querySelector('.carousel-track');
        this.cards = this.track?.children;
        
        if (!this.track || !this.cards?.length) {
            throw new Error('Carousel requires a track element with child items');
        }

        this.nextButton = container.querySelector('.carousel-button.next');
        this.prevButton = container.querySelector('.carousel-button.prev');

        if (!this.nextButton || !this.prevButton) {
            throw new Error('Carousel navigation buttons not found');
        }

        this.cardWidth = this.cards[0].offsetWidth + 30; // Including gap
        this.currentIndex = 0;
        this.isAnimating = false;
        this.isLoading = true;
        this.loadedImages = 0;
        this.totalImages = this.cards.length;
        
        this.init();
    }

    init() {
        this.preloadImages();
        this.nextButton.addEventListener('click', () => this.handleNavigation('next'));
        this.prevButton.addEventListener('click', () => this.handleNavigation('prev'));
        this.updateButtonsState();

        // Add touch support
        this.setupTouchEvents();
        
        // Add keyboard navigation
        this.setupKeyboardEvents();
        
        // Update on window resize
        this.setupResizeHandler();

        // Add intersection observer
        this.setupLazyLoading();
        
        // Smooth scrolling is handled by CSS transitions on .carousel-track
        // this.setupSmoothScrolling(); 
    }

    preloadImages() {
        Array.from(this.cards).forEach(card => {
            const img = card.querySelector('img');
            if (img) {
                if (img.complete) {
                    this.handleImageLoad();
                } else {
                    img.addEventListener('load', () => this.handleImageLoad());
                }
            }
        });
    }

    handleImageLoad() {
        this.loadedImages++;
        if (this.loadedImages === this.totalImages) {
            this.isLoading = false;
            this.container.style.opacity = '1';
        }
    }

    setupLazyLoading() {
        // Custom logic for image loading
        const imageObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        const img = card.querySelector('img[data-src]');

                        if (img) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            img.addEventListener('load', () => {
                                card.classList.add('image-loaded');
                            });
                        }

                        card.classList.remove('card-loading');
                        imageObserver.unobserve(card);
                    }
                });
            },
            { threshold: 0.1, rootMargin: '50px' }
        );

        this.cards.forEach(card => {
            card.classList.add('card-loading');
            imageObserver.observe(card);
        });
    }

    // setupSmoothScrolling() {
    //     let animationFrame;
    //     let targetPosition;
    //     let currentPosition = 0;
    //
    //     const animate = () => {
    //         if (!targetPosition) return;
    //
    //         const diff = targetPosition - currentPosition;
    //         const delta = diff * 0.1;
    //
    //         if (Math.abs(delta) < 0.5) {
    //             currentPosition = targetPosition;
    //             targetPosition = null;
    //         } else {
    //             currentPosition += delta;
    //         }
    //
    //         this.track.style.transform = `translateX(${-currentPosition}px)`;
    //         
    //         if (targetPosition !== null) {
    //             animationFrame = requestAnimationFrame(animate);
    //         }
    //     };
    //
    //     this.smoothScrollTo = (position) => {
    //         targetPosition = position;
    //         cancelAnimationFrame(animationFrame);
    //         animationFrame = requestAnimationFrame(animate);
    //     };
    // }

    handleNavigation(direction) {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        if (direction === 'next') {
            this.next();
        } else {
            this.prev();
        }
        setTimeout(() => this.isAnimating = false, 500);
    }

    next() {
        if (this.currentIndex < this.cards.length - this.getVisibleCards()) {
            this.currentIndex++;
            this.updatePosition();
        }
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.updatePosition();
        }
    }

    getVisibleCards() {
        // Always return at least 1 to avoid disabling navigation
        return Math.max(1, Math.floor(this.container.offsetWidth / this.cardWidth));
    }

    // calculateInitialPosition() {
    //     const containerWidth = this.container.offsetWidth;
    //     const totalCards = this.cards.length;
    //     const cardWidth = this.cardWidth;
    //     const visibleCards = Math.floor(containerWidth / cardWidth);
    //     
    //     // Center the carousel by adjusting the starting position
    //     const offset = (containerWidth - (visibleCards * cardWidth)) / 2;
    //     this.track.style.paddingLeft = `${offset}px`;
    // }

    updatePosition() {
        const position = this.currentIndex * this.cardWidth;
        this.track.style.transform = `translateX(${-position}px)`;
        this.updateButtonsState();
    }

    updateButtonsState() {
        this.prevButton.style.opacity = this.currentIndex === 0 ? '0.5' : '1';
        this.prevButton.style.pointerEvents = this.currentIndex === 0 ? 'none' : 'auto';
        
        const maxIndex = this.cards.length - this.getVisibleCards();
        this.nextButton.style.opacity = this.currentIndex >= maxIndex ? '0.5' : '1';
        this.nextButton.style.pointerEvents = this.currentIndex >= maxIndex ? 'none' : 'auto';
    }

    setupTouchEvents() {
        let startX, moveX;
        const threshold = 50;

        this.track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        }, { passive: true });

        this.track.addEventListener('touchmove', (e) => {
            moveX = e.touches[0].clientX;
        }, { passive: true });

        this.track.addEventListener('touchend', () => {
            if (!startX || !moveX) return;
            
            const diff = startX - moveX;
            if (Math.abs(diff) > threshold) {
                if (diff > 0) this.handleNavigation('next');
                else this.handleNavigation('prev');
            }
            
            startX = null;
            moveX = null;
        });
    }

    setupKeyboardEvents() {
        this.container.setAttribute('tabindex', '0');
        this.container.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                this.handleNavigation('prev');
            }
            if (e.key === 'ArrowRight') {
                e.preventDefault();
                this.handleNavigation('next');
            }
            if (e.key === 'Home') {
                e.preventDefault();
                this.goToFirst();
            }
            if (e.key === 'End') {
                e.preventDefault();
                this.goToLast();
            }
        });

        // Add focus management for individual cards
        this.setupCardKeyboardNavigation();
    }

    setupCardKeyboardNavigation() {
        Array.from(this.cards).forEach((card) => {
            const playButton = card.querySelector('.play-button');
            const actionButton = card.querySelector('.button');

            if (playButton) {
                playButton.addEventListener('keydown', (e) => {
                    if (e.key === 'Tab' && !e.shiftKey) {
                        // Tab to next focusable element within card
                        if (actionButton) {
                            e.preventDefault();
                            actionButton.focus();
                        }
                    }
                });
            }

            if (actionButton) {
                actionButton.addEventListener('keydown', (e) => {
                    if (e.key === 'Tab' && e.shiftKey) {
                        // Shift+Tab to previous focusable element within card
                        if (playButton) {
                            e.preventDefault();
                            playButton.focus();
                        }
                    }
                });
            }
        });
    }

    goToFirst() {
        this.currentIndex = 0;
        this.updatePosition();
    }

    goToLast() {
        this.currentIndex = Math.max(0, this.cards.length - this.getVisibleCards());
        this.updatePosition();
    }

    setupResizeHandler() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.cardWidth = this.cards[0].offsetWidth + 30;
                this.updatePosition();
            }, 250);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const profileButton = document.querySelector('.profile-button');
    const dropdownMenu = document.querySelector('.dropdown');

    if (profileButton && dropdownMenu) {
        profileButton.addEventListener('click', () => {
            const isExpanded = profileButton.getAttribute('aria-expanded') === 'true';
            profileButton.setAttribute('aria-expanded', !isExpanded);
            dropdownMenu.classList.toggle('show');
        });

        document.addEventListener('click', (e) => {
            if (!profileButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.remove('show');
                profileButton.setAttribute('aria-expanded', 'false');
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && dropdownMenu.classList.contains('show')) {
                dropdownMenu.classList.remove('show');
                profileButton.setAttribute('aria-expanded', 'false');
            }
        });
    }

    // Initialize all carousels with a slight delay to ensure proper rendering
    const carouselContainers = document.querySelectorAll('.carousel-container');
    if (carouselContainers.length > 0) {
        carouselContainers.forEach(container => {
            setTimeout(() => {
                new Carousel(container); // Initialize a new Carousel for each container
            }, 100);
        });
    }

    // Initialize mobile navigation
    initializeMobileNavigation();
});

// Mobile Navigation Manager
function initializeMobileNavigation() {
    const hamburger = document.getElementById('hamburger');
    const menu = document.getElementById('menu');
    const overlay = document.getElementById('mobileOverlay');

    if (!hamburger || !menu || !overlay) return;

    let isOpen = false;

    function toggleMenu() {
        if (isOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    function openMenu() {
        isOpen = true;
        hamburger.classList.add('active');
        menu.classList.add('active');
        overlay.classList.add('active');
        hamburger.setAttribute('aria-expanded', 'true');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus first menu item for accessibility
        const firstMenuItem = menu.querySelector('a');
        if (firstMenuItem) {
            setTimeout(() => firstMenuItem.focus(), 300);
        }
    }

    function closeMenu() {
        isOpen = false;
        hamburger.classList.remove('active');
        menu.classList.remove('active');
        overlay.classList.remove('active');
        hamburger.setAttribute('aria-expanded', 'false');

        // Restore body scroll
        document.body.style.overflow = '';

        // Return focus to hamburger button
        hamburger.focus();
    }

    // Event listeners
    hamburger.addEventListener('click', toggleMenu);
    overlay.addEventListener('click', closeMenu);

    // Close menu when clicking on menu links
    menu.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', closeMenu);
    });

    // Close menu on escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && isOpen) {
            closeMenu();
        }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768 && isOpen) {
            closeMenu();
        }
    });
}

// ===== PHASE 3: GLOBAL MOBILE OPTIMIZATIONS & ADVANCED FEATURES =====

// Initialize global mobile optimizations
function initializeMobileOptimizations() {
    // Set viewport height for mobile browsers
    function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);

    // Add mobile class to body
    if (window.innerWidth <= 768) {
        document.body.classList.add('mobile-device');
    }

    // Optimize touch interactions
    if ('ontouchstart' in window) {
        document.body.classList.add('touch-device');
        setupGlobalTouchOptimizations();
    }

    // Setup progressive loading
    setupProgressiveLoading();
}

// Global touch optimizations
function setupGlobalTouchOptimizations() {
    // Improve touch scrolling performance
    document.body.style.webkitOverflowScrolling = 'touch';

    // Add touch feedback to all interactive elements
    const interactiveElements = document.querySelectorAll('button, .card, .quick-link, .nav-link');
    interactiveElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        }, { passive: true });

        element.addEventListener('touchend', function() {
            this.classList.remove('touch-active');
        }, { passive: true });
    });
}

// Progressive loading for better performance
function setupProgressiveLoading() {
    // Lazy load images
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.classList.add('fade-in-up');
                    setTimeout(() => img.classList.add('loaded'), 100);
                    imageObserver.unobserve(img);
                }
            });
        }, { threshold: 0.1, rootMargin: '50px' });

        images.forEach(img => imageObserver.observe(img));
    }
}

// Performance monitoring
function initializePerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
        if ('performance' in window) {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log(`🚀 Page loaded in ${Math.round(perfData.loadEventEnd - perfData.fetchStart)}ms`);

            // Report to analytics (would be implemented in real app)
            if (perfData.loadEventEnd - perfData.fetchStart > 3000) {
                console.warn('⚠️ Slow page load detected');
            }
        }
    });

    // Monitor memory usage (if available)
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                console.warn('⚠️ High memory usage detected');
            }
        }, 30000); // Check every 30 seconds
    }
}

// Cross-platform compatibility
function ensureCrossPlatformCompatibility() {
    // iOS Safari fixes
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.body.classList.add('ios-device');

        // Fix iOS viewport height issue
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }, 500);
        });
    }

    // Android Chrome fixes
    if (/Android/.test(navigator.userAgent)) {
        document.body.classList.add('android-device');
    }

    // Add browser-specific classes
    const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isChrome = /Chrome/.test(navigator.userAgent) && navigator.vendor && navigator.vendor.includes('Google');

    if (isFirefox) document.body.classList.add('firefox');
    if (isSafari) document.body.classList.add('safari');
    if (isChrome) document.body.classList.add('chrome');
}

// Initialize everything when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    initializeMobileOptimizations();
    initializePerformanceMonitoring();
    ensureCrossPlatformCompatibility();
    initializeMiniPlayer();

    console.log('🎵 Banshee Music - Phase 3 Complete: Mobile Optimized & Demo Ready!');
});

// Global Mini Player Implementation
function initializeMiniPlayer() {
    const miniPlayer = document.getElementById('miniPlayer');
    if (!miniPlayer) return; // Exit if no mini player on this page

    console.log('🎵 Initializing Mini Player...');

    // Mini player elements
    const miniPlayerArtwork = document.getElementById('miniPlayerArtwork');
    const miniPlayerTitle = document.getElementById('miniPlayerTitle');
    const miniPlayerArtist = document.getElementById('miniPlayerArtist');
    const miniPlayPauseBtn = document.getElementById('miniPlayPauseBtn');
    const miniPrevBtn = document.getElementById('miniPrevBtn');
    const miniNextBtn = document.getElementById('miniNextBtn');
    const miniProgressFill = document.getElementById('miniProgressFill');
    const miniCurrentTime = document.getElementById('miniCurrentTime');
    const miniDuration = document.getElementById('miniDuration');
    const miniCloseBtn = document.getElementById('miniCloseBtn');

    // Audio and state
    let currentAudio = null;
    let currentTracks = [];
    let currentTrackIndex = -1;

    // Format time helper
    function formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    // Show mini player
    function showMiniPlayer() {
        miniPlayer.classList.remove('hidden');
    }

    // Hide mini player
    function hideMiniPlayer() {
        miniPlayer.classList.add('hidden');
        if (currentAudio) {
            currentAudio.pause();
            currentAudio = null;
        }
    }

    // Update progress
    function updateProgress() {
        if (!currentAudio || !currentAudio.duration) return;

        const progress = (currentAudio.currentTime / currentAudio.duration) * 100;
        if (miniProgressFill) miniProgressFill.style.width = `${progress}%`;

        if (miniCurrentTime) miniCurrentTime.textContent = formatTime(currentAudio.currentTime);
        if (miniDuration) miniDuration.textContent = formatTime(currentAudio.duration);
    }

    // Update play button
    function updatePlayButton() {
        if (!miniPlayPauseBtn) return;

        const icon = miniPlayPauseBtn.querySelector('i');
        if (currentAudio && !currentAudio.paused) {
            icon.classList.remove('fa-play');
            icon.classList.add('fa-pause');
        } else {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
        }
    }

    // Play track
    function playTrack(trackData, trackList = null, trackIndex = 0) {
        console.log('🎵 Playing track:', trackData);

        // Stop current audio
        if (currentAudio) {
            currentAudio.pause();
        }

        // Create new audio
        currentAudio = new Audio(trackData.preview || trackData.src);
        currentTracks = trackList || [trackData];
        currentTrackIndex = trackIndex;

        // Update mini player info
        if (miniPlayerArtwork) miniPlayerArtwork.src = trackData.artwork || trackData.cover || 'imgs/album-01.png';
        if (miniPlayerTitle) miniPlayerTitle.textContent = trackData.title || 'Unknown Track';
        if (miniPlayerArtist) miniPlayerArtist.textContent = trackData.artist || 'Unknown Artist';

        // Bind audio events
        currentAudio.addEventListener('timeupdate', updateProgress);
        currentAudio.addEventListener('loadedmetadata', updateProgress);
        currentAudio.addEventListener('play', updatePlayButton);
        currentAudio.addEventListener('pause', updatePlayButton);
        currentAudio.addEventListener('ended', () => {
            // Auto-play next track if available
            if (currentTrackIndex < currentTracks.length - 1) {
                playTrack(currentTracks[currentTrackIndex + 1], currentTracks, currentTrackIndex + 1);
            } else {
                updatePlayButton();
            }
        });

        // Show mini player and play
        showMiniPlayer();
        currentAudio.play().catch(error => {
            console.error('❌ Failed to play audio:', error);
        });
        updatePlayButton();
    }

    // Mini player controls
    if (miniPlayPauseBtn) {
        miniPlayPauseBtn.addEventListener('click', () => {
            if (currentAudio) {
                if (currentAudio.paused) {
                    currentAudio.play();
                } else {
                    currentAudio.pause();
                }
            }
        });
    }

    if (miniPrevBtn) {
        miniPrevBtn.addEventListener('click', () => {
            if (currentTrackIndex > 0) {
                playTrack(currentTracks[currentTrackIndex - 1], currentTracks, currentTrackIndex - 1);
            }
        });
    }

    if (miniNextBtn) {
        miniNextBtn.addEventListener('click', () => {
            if (currentTrackIndex < currentTracks.length - 1) {
                playTrack(currentTracks[currentTrackIndex + 1], currentTracks, currentTrackIndex + 1);
            }
        });
    }

    if (miniCloseBtn) {
        miniCloseBtn.addEventListener('click', hideMiniPlayer);
    }

    // Make mini player available globally
    window.miniPlayer = {
        playTrack,
        showMiniPlayer,
        hideMiniPlayer,
        isVisible: () => !miniPlayer.classList.contains('hidden')
    };

    console.log('✅ Mini Player initialized successfully');
}
