<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/searchresults.css">
</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>

            <!-- Hamburger <PERSON><PERSON> -->
            <button type="button" class="hamburger" id="hamburger" aria-label="Toggle navigation menu" aria-expanded="false">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <ul class="menu" id="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
    </header>

    <main id="main-content" class="container">
        <div class="search-results-page">
            <!-- Search Header -->
            <div class="search-header">
                <div class="search-bar-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="search" id="searchInput" class="search-input"
                               placeholder="Search for songs, artists, albums, playlists..."
                               aria-label="Search music" autocomplete="off">
                        <button type="button" class="clear-search-btn" id="clearSearchBtn" aria-label="Clear search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <button type="button" class="search-filters-btn" id="searchFiltersBtn" aria-label="Search filters">
                        <i class="fas fa-filter"></i>
                        Filters
                    </button>
                </div>

                <!-- Search Filters Panel -->
                <div class="search-filters-panel hidden" id="searchFiltersPanel">
                    <div class="filters-content">
                        <div class="filter-group">
                            <label for="contentTypeFilter">Content Type</label>
                            <select id="contentTypeFilter" name="contentType">
                                <option value="all">All</option>
                                <option value="songs">Songs</option>
                                <option value="artists">Artists</option>
                                <option value="albums">Albums</option>
                                <option value="playlists">Playlists</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="genreFilter">Genre</label>
                            <select id="genreFilter" name="genre">
                                <option value="">Any Genre</option>
                                <option value="pop">Pop</option>
                                <option value="rock">Rock</option>
                                <option value="hip-hop">Hip Hop</option>
                                <option value="electronic">Electronic</option>
                                <option value="jazz">Jazz</option>
                                <option value="classical">Classical</option>
                                <option value="country">Country</option>
                                <option value="r&b">R&B</option>
                                <option value="indie">Indie</option>
                                <option value="alternative">Alternative</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="sortFilter">Sort By</label>
                            <select id="sortFilter" name="sort">
                                <option value="relevance">Relevance</option>
                                <option value="popularity">Popularity</option>
                                <option value="newest">Newest</option>
                                <option value="oldest">Oldest</option>
                                <option value="alphabetical">A-Z</option>
                                <option value="duration">Duration</option>
                            </select>
                        </div>

                        <div class="filter-actions">
                            <button type="button" class="filter-btn clear-filters-btn" id="clearFiltersBtn">
                                Clear Filters
                            </button>
                            <button type="button" class="filter-btn apply-filters-btn" id="applyFiltersBtn">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Results Content -->
            <div class="search-results-content">
                <!-- Search Query Display -->
                <div class="search-query-info" id="searchQueryInfo">
                    <h1 class="search-title-gradient">Search Results</h1>
                    <p class="search-description">Enter a search term to find your favorite music</p>
                </div>

                <!-- Quick Suggestions (shown when no search) -->
                <div class="quick-suggestions" id="quickSuggestions">
                    <h2>Popular Searches</h2>
                    <div class="suggestion-tags">
                        <button type="button" class="suggestion-tag" data-query="trending hits">Trending Hits</button>
                        <button type="button" class="suggestion-tag" data-query="chill music">Chill Music</button>
                        <button type="button" class="suggestion-tag" data-query="workout songs">Workout Songs</button>
                        <button type="button" class="suggestion-tag" data-query="indie rock">Indie Rock</button>
                        <button type="button" class="suggestion-tag" data-query="electronic">Electronic</button>
                        <button type="button" class="suggestion-tag" data-query="jazz classics">Jazz Classics</button>
                        <button type="button" class="suggestion-tag" data-query="pop hits">Pop Hits</button>
                        <button type="button" class="suggestion-tag" data-query="acoustic">Acoustic</button>
                    </div>
                </div>

                <!-- Search Results Sections -->
                <div class="search-results-sections hidden" id="searchResultsSections">
                    <!-- Top Result -->
                    <section class="top-result-section" id="topResultSection">
                        <h2>Top Result</h2>
                        <div class="top-result-card" id="topResultCard">
                            <!-- Top result will be inserted here -->
                        </div>
                    </section>

                    <!-- Songs Section -->
                    <section class="results-section songs-section" id="songsSection">
                        <div class="section-header">
                            <h2>Songs</h2>
                            <button type="button" class="view-all-btn" data-type="songs">View All</button>
                        </div>
                        <div class="results-grid songs-grid" id="songsGrid">
                            <!-- Song results will be inserted here -->
                        </div>
                    </section>

                    <!-- Artists Section -->
                    <section class="results-section artists-section" id="artistsSection">
                        <div class="section-header">
                            <h2>Artists</h2>
                            <button type="button" class="view-all-btn" data-type="artists">View All</button>
                        </div>
                        <div class="results-grid artists-grid" id="artistsGrid">
                            <!-- Artist results will be inserted here -->
                        </div>
                    </section>

                    <!-- Albums Section -->
                    <section class="results-section albums-section" id="albumsSection">
                        <div class="section-header">
                            <h2>Albums</h2>
                            <button type="button" class="view-all-btn" data-type="albums">View All</button>
                        </div>
                        <div class="results-grid albums-grid" id="albumsGrid">
                            <!-- Album results will be inserted here -->
                        </div>
                    </section>

                    <!-- Playlists Section -->
                    <section class="results-section playlists-section" id="playlistsSection">
                        <div class="section-header">
                            <h2>Playlists</h2>
                            <button type="button" class="view-all-btn" data-type="playlists">View All</button>
                        </div>
                        <div class="results-grid playlists-grid" id="playlistsGrid">
                            <!-- Playlist results will be inserted here -->
                        </div>
                    </section>
                </div>

                <!-- No Results State -->
                <div class="no-results-state hidden" id="noResultsState">
                    <div class="no-results-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>No results found</h3>
                    <p>Try adjusting your search or filters to find what you're looking for</p>
                    <div class="no-results-suggestions">
                        <h4>Suggestions:</h4>
                        <ul>
                            <li>Check your spelling</li>
                            <li>Try different keywords</li>
                            <li>Use fewer filters</li>
                            <li>Search for similar artists or songs</li>
                        </ul>
                    </div>
                </div>

                <!-- Loading State -->
                <div class="loading-state hidden" id="loadingState">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <p>Searching for your music...</p>
                </div>
            </div>
        </div>

        <div id="aria-live-region" aria-live="polite" class="sr-only"></div>
    </main>

    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>

            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">0:00</span>
                </div>
            </div>

            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Shared utilities -->
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/services/deezer-service.js"></script>
    <script src="js/services/jamendo-service.js"></script>
    <script src="js/services/hybrid-music-service.js"></script>
    <script src="js/searchresults.js"></script>

    <!-- Search Results Mini Player Integration -->
    <script>
        // Search Results Mini Player Integration
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for global mini player to be initialized
            setTimeout(() => {
                if (window.miniPlayer) {
                    console.log('🎵 Search Results page mini player integration ready');

                    // Listen for play buttons in search results
                    document.addEventListener('click', (e) => {
                        const playBtn = e.target.closest('.play-btn, .result-play-btn');
                        if (playBtn && playBtn.dataset.preview) {
                            e.preventDefault();
                            e.stopPropagation();

                            const resultItem = playBtn.closest('.result-item, .track-item, .album-item');
                            const trackData = {
                                preview: playBtn.dataset.preview,
                                title: playBtn.dataset.title || resultItem?.querySelector('.result-title, .track-title')?.textContent || 'Unknown Track',
                                artist: playBtn.dataset.artist || resultItem?.querySelector('.result-artist, .track-artist')?.textContent || 'Unknown Artist',
                                artwork: playBtn.dataset.artwork || resultItem?.querySelector('img')?.src || 'imgs/album-01.png'
                            };

                            // Use global mini player
                            window.miniPlayer.playTrack(trackData);
                        }
                    });
                } else {
                    console.warn('⚠️ Global mini player not available');
                }
            }, 100);
        });
    </script>
</body>
</html>