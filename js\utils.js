// Shared utilities for Banshee Music App
// Consolidates common functionality used across multiple pages

class BansheeUtils {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.liveRegion = this.createLiveRegion();
        this.cache = new Map();
        this.performanceMetrics = new Map();

        // Circuit breaker state
        this.circuitBreaker = {
            failures: 0,
            lastFailureTime: 0,
            state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
            threshold: 5,
            timeout: 30000 // 30 seconds
        };

        // Health monitoring
        this.healthStatus = {
            api: 'unknown',
            deezer: 'unknown',
            lastCheck: 0
        };

        this.init();
    }

    // ===== INITIALIZATION =====
    init() {
        this.startCacheCleanup();
        this.startHealthMonitoring();
        this.preloadCriticalResources();
        console.log('🎵 Banshee Utils initialized with performance optimizations & resilience');
    }

    startCacheCleanup() {
        // Clean cache every 10 minutes
        setInterval(() => {
            this.cleanupCache();
        }, 600000);
    }

    startHealthMonitoring() {
        // Perform health check every 5 minutes
        setInterval(() => {
            this.performHealthCheck();
        }, 300000);

        // Initial health check after 5 seconds
        setTimeout(() => {
            this.performHealthCheck();
        }, 5000);
    }

    async preloadCriticalResources() {
        try {
            // Preload Deezer service if available
            if (window.deezerService) {
                await window.deezerService.preloadPopularContent();
            }

            // Preload critical API endpoints with circuit breaker
            const criticalEndpoints = [
                { url: '/health', fallback: null, options: { useCache: false, retries: 1 } }
            ];

            await this.fetchMultiple(criticalEndpoints, { batchSize: 1 });
            console.log('✅ Critical resources preloaded');
        } catch (error) {
            console.warn('⚠️ Failed to preload resources:', error);
        }
    }

    // ===== ACCESSIBILITY UTILITIES =====
    createLiveRegion() {
        let liveRegion = document.getElementById('aria-live-region');
        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = 'aria-live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.style.position = 'absolute';
            liveRegion.style.left = '-10000px';
            liveRegion.style.width = '1px';
            liveRegion.style.height = '1px';
            liveRegion.style.overflow = 'hidden';
            document.body.appendChild(liveRegion);
        }
        return liveRegion;
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;
        }
    }

    // ===== INTERSECTION OBSERVER UTILITIES =====
    createFadeInObserver(options = {}) {
        const defaultOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px',
            staggerDelay: 100
        };
        const config = { ...defaultOptions, ...options };

        return new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * config.staggerDelay);
                }
            });
        }, {
            threshold: config.threshold,
            rootMargin: config.rootMargin
        });
    }

    observeElements(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const observer = this.createFadeInObserver(options);

        elements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(element);
        });

        return observer;
    }

    // ===== API UTILITIES WITH ENHANCED ERROR HANDLING =====
    async fetchWithFallback(endpoint, fallbackData = null, options = {}) {
        const {
            useCache = true,
            cacheTimeout = 300000, // 5 minutes default
            retries = 3,
            retryDelay = 1000,
            timeout = 10000 // 10 seconds
        } = options;

        // Check cache first
        if (useCache && this.cache.has(endpoint)) {
            const cached = this.cache.get(endpoint);
            if (Date.now() - cached.timestamp < cacheTimeout) {
                console.log(`🎵 Using cached data for: ${endpoint}`);
                return cached.data;
            }
        }

        let lastError;

        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(`${this.apiBase}${endpoint}`, {
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    const data = await response.json();

                    // Cache the result
                    if (useCache) {
                        this.cache.set(endpoint, {
                            data,
                            timestamp: Date.now()
                        });
                    }

                    if (attempt > 1) {
                        console.log(`✅ API call succeeded on attempt ${attempt} for: ${endpoint}`);
                    }

                    return data;
                }

                throw new Error(`HTTP ${response.status}: ${response.statusText}`);

            } catch (error) {
                lastError = error;

                if (error.name === 'AbortError') {
                    console.warn(`⏱️ API call timed out for ${endpoint} (attempt ${attempt})`);
                } else {
                    console.warn(`⚠️ API call failed for ${endpoint} (attempt ${attempt}):`, error.message);
                }

                // Don't retry on certain errors
                if (error.name === 'AbortError' || (error.message.includes('404'))) {
                    break;
                }

                // Wait before retrying (exponential backoff)
                if (attempt < retries) {
                    const delay = retryDelay * Math.pow(2, attempt - 1);
                    await this.delay(delay);
                }
            }
        }

        // All retries failed, use fallback
        if (fallbackData) {
            console.log(`📦 Using fallback data for ${endpoint} after ${retries} failed attempts`);
            return fallbackData;
        }

        throw lastError || new Error(`Failed to fetch ${endpoint} after ${retries} attempts`);
    }

    // ===== CIRCUIT BREAKER PATTERN =====
    async fetchWithCircuitBreaker(endpoint, fallbackData = null, options = {}) {
        // Check circuit breaker state
        if (this.circuitBreaker.state === 'OPEN') {
            const timeSinceLastFailure = Date.now() - this.circuitBreaker.lastFailureTime;
            if (timeSinceLastFailure < this.circuitBreaker.timeout) {
                console.warn(`🚫 Circuit breaker OPEN for ${endpoint}, using fallback`);
                if (fallbackData) return fallbackData;
                throw new Error('Circuit breaker is OPEN');
            } else {
                // Try to transition to HALF_OPEN
                this.circuitBreaker.state = 'HALF_OPEN';
                console.log('🔄 Circuit breaker transitioning to HALF_OPEN');
            }
        }

        try {
            const result = await this.fetchWithFallback(endpoint, fallbackData, options);

            // Success - reset circuit breaker
            if (this.circuitBreaker.state === 'HALF_OPEN') {
                this.circuitBreaker.state = 'CLOSED';
                this.circuitBreaker.failures = 0;
                console.log('✅ Circuit breaker reset to CLOSED');
            }

            return result;

        } catch (error) {
            // Failure - update circuit breaker
            this.circuitBreaker.failures++;
            this.circuitBreaker.lastFailureTime = Date.now();

            if (this.circuitBreaker.failures >= this.circuitBreaker.threshold) {
                this.circuitBreaker.state = 'OPEN';
                console.warn(`🚫 Circuit breaker OPEN after ${this.circuitBreaker.failures} failures`);
            }

            throw error;
        }
    }

    // ===== HEALTH MONITORING =====
    async checkApiHealth() {
        try {
            const response = await fetch(`${this.apiBase}/health`, {
                method: 'GET',
                timeout: 5000
            });

            this.healthStatus.api = response.ok ? 'healthy' : 'unhealthy';
            this.healthStatus.lastCheck = Date.now();

            return this.healthStatus.api === 'healthy';
        } catch (error) {
            this.healthStatus.api = 'unhealthy';
            this.healthStatus.lastCheck = Date.now();
            return false;
        }
    }

    async checkDeezerHealth() {
        try {
            if (window.deezerService) {
                // Try a simple Deezer API call
                await window.deezerService.makeRequest('chart', 'health_check');
                this.healthStatus.deezer = 'healthy';
            } else {
                this.healthStatus.deezer = 'unavailable';
            }
        } catch (error) {
            this.healthStatus.deezer = 'unhealthy';
        }

        return this.healthStatus.deezer === 'healthy';
    }

    async performHealthCheck() {
        const [apiHealthy, deezerHealthy] = await Promise.all([
            this.checkApiHealth(),
            this.checkDeezerHealth()
        ]);

        console.log(`🏥 Health Check - API: ${this.healthStatus.api}, Deezer: ${this.healthStatus.deezer}`);

        return {
            api: this.healthStatus.api,
            deezer: this.healthStatus.deezer,
            overall: apiHealthy || deezerHealthy ? 'operational' : 'degraded'
        };
    }

    async fetchMultiple(endpoints, options = {}) {
        const { batchSize = 5, delay = 100, useCircuitBreaker = true } = options;
        const results = [];

        // Process in batches to avoid overwhelming the server
        for (let i = 0; i < endpoints.length; i += batchSize) {
            const batch = endpoints.slice(i, i + batchSize);
            const promises = batch.map(endpoint => {
                const fetchMethod = useCircuitBreaker ?
                    this.fetchWithCircuitBreaker.bind(this) :
                    this.fetchWithFallback.bind(this);
                return fetchMethod(endpoint.url, endpoint.fallback, endpoint.options);
            });

            try {
                const batchResults = await Promise.allSettled(promises);
                results.push(...batchResults);

                // Add delay between batches
                if (i + batchSize < endpoints.length && delay > 0) {
                    await this.delay(delay);
                }
            } catch (error) {
                console.error('❌ Batch fetch failed:', error);
                // Add failed results as rejected promises
                results.push(...batch.map(() => ({ status: 'rejected', reason: error })));
            }
        }

        return results;
    }

    // Simplified API method that uses circuit breaker by default
    async apiCall(endpoint, fallbackData = null, options = {}) {
        return this.fetchWithCircuitBreaker(endpoint, fallbackData, options);
    }

    // Performance optimization utilities
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Lazy loading utility
    createLazyLoader(selector, callback, options = {}) {
        const { threshold = 0.1, rootMargin = '50px' } = options;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    callback(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold, rootMargin });

        document.querySelectorAll(selector).forEach(el => {
            observer.observe(el);
        });

        return observer;
    }

    // Memory management
    clearCache() {
        this.cache.clear();
        console.log('🧹 Cache cleared');
    }

    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            memoryUsage: this.cache.size * 1024 // Rough estimate
        };
    }

    // Cleanup old cache entries
    cleanupCache(maxAge = 600000) { // 10 minutes default
        const now = Date.now();
        let cleaned = 0;
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > maxAge) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log(`🧹 Cleaned ${cleaned} expired cache entries`);
        }
    }

    // ===== PERFORMANCE MONITORING =====
    startPerformanceTimer(label) {
        this.performanceMetrics.set(label, performance.now());
    }

    endPerformanceTimer(label) {
        const startTime = this.performanceMetrics.get(label);
        if (startTime) {
            const duration = performance.now() - startTime;
            this.performanceMetrics.delete(label);
            console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
            return duration;
        }
        return null;
    }

    // Monitor API response times
    async monitoredFetch(endpoint, fallbackData = null, options = {}) {
        const label = `API: ${endpoint}`;
        this.startPerformanceTimer(label);

        try {
            const result = await this.fetchWithFallback(endpoint, fallbackData, options);
            this.endPerformanceTimer(label);
            return result;
        } catch (error) {
            this.endPerformanceTimer(label);
            throw error;
        }
    }

    // Get performance report
    getPerformanceReport() {
        return {
            cacheStats: this.getCacheStats(),
            activeTimers: Array.from(this.performanceMetrics.keys()),
            memoryUsage: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : 'Not available'
        };
    }

    // ===== ANIMATION UTILITIES =====
    animateNumber(element, targetValue, duration = 1000) {
        if (!element) return;

        const startValue = parseInt(element.textContent) || 0;
        const difference = targetValue - startValue;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const currentValue = Math.round(startValue + (difference * easeOut));
            
            element.textContent = this.formatNumber(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    // ===== DOM UTILITIES =====
    createElement(tag, className = '', content = '') {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (content) element.innerHTML = content;
        return element;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // ===== CARD RENDERING UTILITIES =====
    renderTrackCard(track, type = 'default') {
        const playButton = `
            <div class="play-overlay">
                <button type="button" class="play-button" aria-label="Play ${track.title}">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        `;

        return `
            <div class="carousel-card" data-track-id="${track.id}">
                <div class="card">
                    <div class="img-container">
                        <img src="${track.image || track.album?.cover_medium || 'imgs/album-01.png'}" 
                             alt="${track.title}" loading="lazy" />
                        ${playButton}
                    </div>
                    <div class="card-content">
                        <div class="text-content">
                            <h3>${track.title}</h3>
                            <p>Artist: ${track.artist?.name || track.artist} • ${track.duration ? this.formatDuration(track.duration) : 'Unknown'}</p>
                        </div>
                        <a href="#" class="button">Play Now</a>
                    </div>
                </div>
            </div>
        `;
    }

    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // ===== EVENT HANDLING UTILITIES =====
    bindPlayButtons(container, callback) {
        if (!container) return;
        
        container.querySelectorAll('.play-button, .play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('[data-track-id], .card, .result-card');
                const trackId = card?.dataset.trackId || card?.dataset.id;
                if (callback && trackId) {
                    callback(trackId, card);
                }
            });
        });
    }

    // ===== LOADING STATES =====
    showLoading(container, message = 'Loading...') {
        if (!container) return;
        container.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
    }

    hideLoading(container) {
        if (!container) return;
        const loadingState = container.querySelector('.loading-state');
        if (loadingState) {
            loadingState.remove();
        }
    }

    // ===== ERROR HANDLING =====
    showError(container, message = 'Something went wrong') {
        if (!container) return;
        container.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button type="button" class="retry-btn">Try Again</button>
            </div>
        `;
    }
}

// Create global instance
console.log('🎵 Creating global BansheeUtils instance');
window.bansheeUtils = new BansheeUtils();
console.log('🎵 BansheeUtils instance created and available globally');

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BansheeUtils;
}
