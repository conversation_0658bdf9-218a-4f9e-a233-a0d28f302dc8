:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}

/* Navbar Styles */
header {
     background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Skip link for accessibility */
.skip-link {
    position: absolute;
    left: -999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--neon-blue);
    color: #fff;
    z-index: 3000;
    padding: 0.5em 1em;
    border-radius: 8px;
    font-weight: 700;
    transition: left 0.2s;
}

.skip-link:focus {
    left: 10px;
    top: 10px;
    width: auto;
    height: auto;
    outline: 2px solid var(--cosmic-pink);
}

/* Main container styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 20px 40px;
    min-height: 100vh;
}

/* Create playlist page layout */
.create-playlist-page {
    width: 100%;
}

/* Page header */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.playlist-title-gradient {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    letter-spacing: 1.5px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
}

.page-description {
    color: var(--text-secondary);
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Playlist creation container */
.playlist-creation-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

/* Section headers */
.section-header {
    margin-bottom: 1.5rem;
}

.section-header h2 {
    color: var(--text-primary);
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header h2 i {
    color: var(--neon-blue);
}

/* Playlist details section */
.playlist-details-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
}

/* Form styles */
.create-playlist-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    position: relative;
}

.form-group label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.optional {
    color: var(--text-secondary);
    font-weight: 400;
    font-size: 0.85rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-normal);
    resize: none;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 3px rgba(0, 224, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.form-group textarea {
    min-height: 100px;
    font-family: inherit;
}

.form-help {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.character-count {
    position: absolute;
    bottom: 0.5rem;
    right: 0.75rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
    pointer-events: none;
}

.character-count.warning {
    color: #ffa500;
}

.character-count.error {
    color: var(--error-color);
}

/* Form actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Action buttons */
.action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.create-btn {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border: none;
    color: white;
    font-weight: 700;
}

.create-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.secondary-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--text-secondary);
}

/* Playlist cover section */
.playlist-cover-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    height: fit-content;
}

.cover-upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.cover-preview {
    width: 200px;
    height: 200px;
    border-radius: 12px;
    overflow: hidden;
    border: 2px dashed rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    position: relative;
}

.cover-preview:hover {
    border-color: var(--neon-blue);
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.2);
}

.cover-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.default-cover {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-align: center;
}

.default-cover i {
    font-size: 2.5rem;
    opacity: 0.5;
}

.cover-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.upload-btn {
    background: linear-gradient(45deg, var(--electric-violet), var(--cosmic-pink));
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    border: none;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(111, 0, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.cover-help {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Cover overlay for remove button */
.cover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.cover-preview:hover .cover-overlay {
    opacity: 1;
}

.remove-cover-btn {
    background: var(--error-color);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    font-size: 1.2rem;
}

.remove-cover-btn:hover {
    background: #ff6666;
    transform: scale(1.1);
}

/* Form messages */
.form-message {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    margin-top: 2rem;
    animation: slideInUp 0.5s ease-out;
}

.success-message {
    background: linear-gradient(90deg, rgba(167, 255, 74, 0.1), rgba(0, 224, 255, 0.1));
    border: 1px solid var(--cyber-lime);
    color: var(--cyber-lime);
}

.error-message {
    background: linear-gradient(90deg, rgba(255, 70, 70, 0.1), rgba(255, 0, 110, 0.1));
    border: 1px solid var(--error-color);
    color: var(--error-color);
}

.form-message i {
    font-size: 1.2rem;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute !important;
    left: -9999px !important;
    top: auto !important;
    width: 1px !important;
    height: 1px !important;
    overflow: hidden !important;
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading state */
.action-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.action-btn.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Form validation styles */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(255, 70, 70, 0.1);
}

.form-group .error-message {
    color: var(--error-color);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.form-group .error-message i {
    font-size: 0.75rem;
}

/* Name availability indicator */
.name-availability {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

.name-availability.available {
    background-color: rgba(167, 255, 74, 0.1);
    color: var(--cyber-lime);
    border: 1px solid rgba(167, 255, 74, 0.3);
}

.name-availability.unavailable {
    background-color: rgba(255, 70, 70, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 70, 70, 0.3);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .playlist-creation-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .playlist-cover-section {
        order: -1;
    }

    .cover-upload-area {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .cover-preview {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 100px 15px 30px;
    }

    .playlist-title-gradient {
        font-size: 2rem;
    }

    .page-description {
        font-size: 1rem;
    }

    .playlist-details-section,
    .playlist-cover-section {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .action-btn {
        justify-content: center;
        padding: 1rem 1.5rem;
    }

    .cover-upload-area {
        flex-direction: column;
        gap: 1rem;
    }

    .cover-preview {
        width: 120px;
        height: 120px;
    }

    .cover-actions {
        flex-direction: column;
        width: 100%;
    }

    .upload-btn,
    .cover-actions .action-btn {
        justify-content: center;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 90px 10px 20px;
    }

    .playlist-title-gradient {
        font-size: 1.8rem;
    }

    .playlist-details-section,
    .playlist-cover-section {
        padding: 1rem;
    }

    .section-header h2 {
        font-size: 1.2rem;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 0.6rem 0.75rem;
        font-size: 0.95rem;
    }

    .action-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .cover-preview {
        width: 100px;
        height: 100px;
    }

    .default-cover i {
        font-size: 2rem;
    }

    .form-message {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Focus styles for accessibility */
.action-btn:focus,
.upload-btn:focus,
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .playlist-details-section,
    .playlist-cover-section {
        border: 2px solid var(--text-primary);
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        border: 2px solid var(--text-primary);
    }

    .action-btn {
        border: 2px solid var(--text-primary);
    }

    .create-btn {
        background: var(--text-primary);
        color: var(--background-primary);
    }
}

/* Print styles */
@media print {
    .playlist-cover-section,
    .form-actions {
        display: none;
    }

    .playlist-creation-container {
        grid-template-columns: 1fr;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        border: 1px solid #000;
        background: white;
        color: black;
    }
}

