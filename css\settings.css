:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    --button-gradient: var(--gradient-primary);
    --button-gradient-start: var(--neon-blue);
    --button-gradient-end: var(--cosmic-pink);
    --danger-gradient: linear-gradient(45deg, #FF5252, #FF1744);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    --button-shadow: var(--shadow-button);
    --button-hover-shadow: var(--shadow-button-hover);
    
    /* Borders & Radius */
    --settings-card-bg: rgba(20, 22, 30, 0.95);
    --settings-border: 1px solid rgba(255,255,255,0.08);
    --glass-bg: rgba(30, 32, 40, 0.7);
    --border-light: rgba(255,255,255,0.12);
    --border-radius-md: 12px;
    --border-radius-sm: 8px;
    --container-padding: 30px;
    --element-spacing: 24px;
    --section-spacing: 40px;
    --text-muted: rgba(255,255,255,0.5);
    --input-focus-glow: 0 0 0 2px rgba(0,224,255,0.2);
    --button-text-color: #fff;
    --opacity-80: 0.8;
    --text-color: var(--text-primary);
    --background-color: var(--background-primary);
    --transition-speed: 0.3s;
    --transition-timing: ease;
}

body {
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 400;
    line-height: 1.5;
}

header {
    background: var(--header-gradient);
    padding: 12px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--button-shadow);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    box-sizing: border-box;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 90px;
    height: 90px;
    transition: transform var(--transition-speed);
}

.logo img:hover {
    transform: scale(1.05);
}

.menu {
    list-style: none;
    display: flex;
    gap: 3rem;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.menu a:hover {
    color: var(--accent-color);
}

.menu a[aria-current="page"] {
    background: var(--button-gradient);
    color: white;
}

/* Update dropdown styles */
.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--header-gradient-start);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    display: none;
}

.profile-button[aria-expanded="true"] + .dropdown {
    display: block;
}

/* Add JavaScript to settings.js to handle dropdown toggle */
.user-profile {
    position: relative;
}

.profile-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid transparent;
    transition: border-color 0.3s ease, transform 0.3s ease;
}

.profile-button:hover .profile-icon {
    border-color: var(--accent-color);
    transform: scale(1.05);
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    display: none;
    overflow: hidden;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 8px 0;
}

.dropdown ul li {
    margin: 0;
    padding: 0;
}

.dropdown ul li a {
    color: #fff;
    text-decoration: none;
    padding: 10px 20px;
    display: block;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.dropdown ul li a:hover {
    background-color: var(--accent-color);
    color: #fff;
}

.dropdown ul li a[aria-current="page"] {
    background-color: rgba(255, 255, 255, 0.1);
    font-weight: 500;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 160px auto 0;
    padding: 20px;
}

.settings-section {
    background: var(--settings-card-bg);
    border: var(--settings-border);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.settings-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
}

.settings-section h2 {
    font-size: 1.5rem;
    margin-bottom: 25px;
    color: var(--text-color);
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 10px;
    position: relative;
}

.settings-section h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--button-gradient-start), var(--button-gradient-end));
}

.settings-section h3 {
    color: #ff4136;
    margin: 30px 0 15px;
    font-size: 1.2rem;
}

.settings-section form {
    max-width: 500px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

input[type="text"],
input[type="email"],
input[type="password"],
select {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    background-color: #2c2c2e;
    border: 2px solid transparent;
    border-radius: 8px;
    color: #fff;
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: var(--input-focus-glow);
}

select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 40px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

button {
    font-weight: 500;
    padding: 12px 24px;
    font-size: 0.95rem;
    letter-spacing: 0.3px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--button-gradient);
    color: var(--button-text-color);
}

button:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.danger-button {
    background: var(--danger-gradient);
    color: white;
    margin-bottom: 25px;
}

.danger-button:hover {
    background: linear-gradient(45deg, #FF5252, #FF1744);
    box-shadow: 0 4px 15px rgba(255, 0, 110, 0.3);
}

.success-message,
.error-message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

.success-message {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
    color: white;
    border-left: 4px solid #2E7D32;
}

.error-message {
    background: linear-gradient(45deg, #f44336, #FF5252);
    color: white;
    border-left: 4px solid #B71C1C;
}

/* Loader Styles */
.loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid var(--accent-color);
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility Styles */
.skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--accent-color);
    color: white;
    padding: 8px;
    z-index: 100;
}

.skip-link:focus {
    top: 0;
}

/* Settings-specific styles */
.username-availability {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

.username-availability.available {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.username-availability.unavailable {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.field-error {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
    border-radius: 4px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: slideIn 0.3s ease-out;
}

.settings-message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.settings-message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.settings-message.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.settings-message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.settings-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 10000;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Styles */
@media (max-width: 768px) {
    .menu {
        flex-direction: column;
        align-items: center;
    }

    .menu li {
        margin-bottom: 10px;
        margin-right: 0;
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1200px;
    }
}

/* Media Query for Tablet Viewport */
@media (max-width: 1024px) and (min-width: 769px) {
    .container {
        width: 90%;
    }
}

/* Media Query for Mobile Viewport */
@media (max-width: 768px) {
    .logo img {
        width: 80px;
        height: 80px;
    }
}

/* Responsive Navbar */
@media screen and (max-width: 768px) {
    header {
        padding: 10px 15px;
    }

    nav {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .logo img {
        width: 60px;
        height: 60px;
    }

    .menu {
        flex-direction: row;
        gap: 1.5rem;
    }

    .menu li a {
        font-size: 1rem;
    }

    .user-profile img {
        width: 35px;
        height: 35px;
    }

    .dropdown {
        min-width: 160px;
        margin-top: 6px;
    }

    .dropdown ul li a {
        padding: 8px 16px;
        font-size: 0.85rem;
    }
}

@media screen and (max-width: 480px) {
    .logo img {
        width: 50px;
        height: 50px;
    }

    .menu {
        gap: 1rem;
    }

    .menu li a {
        font-size: 0.9rem;
    }

    .user-profile img {
        width: 30px;
        height: 30px;
    }
}

/* Settings Page Styles */
.settings-container {
    padding-top: var(--section-spacing);
    max-width: 800px;
    margin: 0 auto;
}

.settings-header {
    text-align: center;
    margin-bottom: var(--element-spacing);
}

.settings-header h1 {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: calc(var(--element-spacing) / 2);
    background: var(--button-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: var(--text-shadow);
}

.settings-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, var(--opacity-80));
    max-width: 600px;
    margin: 0 auto;
}

.settings-section {
    background: var(--glass-bg);
    border-radius: var(--border-radius-md);
    padding: var(--container-padding);
    border: 1px solid var(--border-light);
    margin-bottom: var(--element-spacing);
}

.settings-section h2 {
    font-size: 1.5rem;
    margin-bottom: var(--element-spacing);
    color: var(--text-color);
}

.settings-group {
    margin-bottom: var(--element-spacing);
}

.settings-group:last-child {
    margin-bottom: 0;
}

.settings-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.settings-group input,
.settings-group select {
    width: 100%;
    padding: 12px;
    background: var(--glass-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-sm);
    color: var(--text-color);
    font-size: 1rem;
    transition: all var(--transition-speed) var(--transition-timing);
}

.settings-group input:focus,
.settings-group select:focus {
    outline: none;
    border-color: var(--neon-blue);
    background: rgba(44, 44, 46, 0.8);
    box-shadow: var(--input-focus-glow);
}

.settings-group input::placeholder {
    color: var(--text-muted);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(44, 44, 46, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 3px;
    background: white;
    transition: all 0.3s ease;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background: var(--button-gradient);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.save-button {
    width: 100%;
    padding: 1rem;
    background: var(--button-gradient);
    color: var(--button-text-color);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-timing);
    box-shadow: var(--button-shadow);
}

.save-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

.save-button:active {
    transform: translateY(0);
}

/* Animation keyframes moved from JS to CSS */
@keyframes slideIn {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(-100%); opacity: 0; }
}

/* Improved message styling */
.success-message,
.error-message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out;
}

.success-message {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
    color: white;
    border-left: 4px solid #2E7D32;
}

.error-message {
    background: linear-gradient(45deg, #f44336, #FF5252);
    color: white;
    border-left: 4px solid #B71C1C;
}

/* Improved form styling */
.settings-group input:focus,
.settings-group select:focus {
    outline: none;
    border-color: var(--neon-blue);
    background: rgba(44, 44, 46, 0.8);
    box-shadow: var(--input-focus-glow);
}

/* Consistent button styling with reduced width */
.save-button, 
button[type="submit"] {
    background: var(--button-gradient);
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
    width: auto; /* Changed from 100% to auto */
    display: inline-block; /* Added to respect width */
    min-width: 150px; /* Added minimum width */
    text-align: center; /* Center text in button */
}

.save-button:hover,
button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 224, 255, 0.3);
}

.danger-button {
    background: var(--danger-gradient);
    color: white;
    margin-bottom: 25px;
}

.danger-button:hover {
    background: linear-gradient(45deg, #FF5252, #FF1744);
    box-shadow: 0 4px 15px rgba(255, 0, 110, 0.3);
}

/* Improved toggle switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(44, 44, 46, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 3px;
    background: white;
    transition: all 0.3s ease;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background: var(--button-gradient);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Add a container for the save button to align it */
.button-container {
    margin-top: 20px;
    text-align: right; /* Align button to the right */
}

/* User profile styling to match Home Page exactly */
.user-profile {
    position: relative;
    margin-left: 20px;
}

.profile-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.profile-button:hover .profile-icon,
.profile-button:focus .profile-icon {
    border-color: var(--cosmic-pink);
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.4);
}

.profile-button[aria-expanded="true"] .profile-icon {
    border-color: var(--cosmic-pink);
    box-shadow: 0 0 15px rgba(255, 0, 110, 0.4);
}

/* Dropdown styling to match Home Page */
.dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: rgba(26, 29, 36, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    z-index: 1000;
    display: none;
    overflow: hidden;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform-origin: top right;
    animation: dropdownFadeIn 0.2s ease-out forwards;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 8px 0;
}

.dropdown ul li {
    margin: 0;
    padding: 0;
}

.dropdown ul li a {
    color: var(--text-color);
    text-decoration: none;
    padding: 12px 20px;
    display: block;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.dropdown ul li a:hover {
    background-color: rgba(255, 0, 110, 0.15);
    color: var(--cosmic-pink);
}

.dropdown ul li a[aria-current="page"] {
    background-color: rgba(255, 0, 110, 0.1);
    color: var(--cosmic-pink);
}

.dropdown ul li a.logout {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #ff4d6d;
}

.dropdown ul li a.logout:hover {
    background-color: rgba(255, 77, 109, 0.15);
}

.profile-picture-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
}

.profile-picture-preview {
    margin-bottom: 10px;
}

#profile-picture-img {
    border-radius: 50%;
    object-fit: cover;
    width: 80px;
    height: 80px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.section-description {
    color: var(--text-secondary);
    font-size: 1em;
    margin-bottom: 18px;
    margin-top: -10px;
}

