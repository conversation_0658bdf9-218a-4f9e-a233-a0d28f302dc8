<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <title>Mini Player Test - Banshee Music</title>
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .test-button {
            background: #00ff88;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        .test-button:hover {
            background: #00cc6a;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: #2d5a2d; }
        .status.error { background: #5a2d2d; }
        .status.warning { background: #5a5a2d; }
        .track-item {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .track-item img {
            width: 60px;
            height: 60px;
            border-radius: 8px;
        }
        .track-info {
            flex: 1;
        }
        .track-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .track-artist {
            color: #ccc;
        }
    </style>
</head>
<body>
    <h1>🎵 Mini Player Test Page</h1>
    <p>Test the global mini player functionality across all pages</p>

    <div class="test-section">
        <h2>Mini Player Status</h2>
        <button class="test-button" onclick="checkMiniPlayerStatus()">Check Status</button>
        <div id="status-results"></div>
    </div>

    <div class="test-section">
        <h2>Test Tracks</h2>
        <p>Click play buttons to test mini player functionality:</p>
        
        <div class="track-item">
            <img src="imgs/album-01.png" alt="Track 1">
            <div class="track-info">
                <div class="track-title">Demo Track 1</div>
                <div class="track-artist">Test Artist</div>
            </div>
            <button class="test-button track-play-btn" 
                    data-preview="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3"
                    data-title="Demo Track 1"
                    data-artist="Test Artist"
                    data-artwork="imgs/album-01.png">
                ▶ Play
            </button>
        </div>

        <div class="track-item">
            <img src="imgs/album-02.png" alt="Track 2">
            <div class="track-info">
                <div class="track-title">Demo Track 2</div>
                <div class="track-artist">Another Artist</div>
            </div>
            <button class="test-button track-play-btn" 
                    data-preview="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3"
                    data-title="Demo Track 2"
                    data-artist="Another Artist"
                    data-artwork="imgs/album-02.png">
                ▶ Play
            </button>
        </div>

        <div class="track-item">
            <img src="imgs/album-03.png" alt="Track 3">
            <div class="track-info">
                <div class="track-title">Demo Track 3</div>
                <div class="track-artist">Third Artist</div>
            </div>
            <button class="test-button track-play-btn" 
                    data-preview="https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3"
                    data-title="Demo Track 3"
                    data-artist="Third Artist"
                    data-artwork="imgs/album-03.png">
                ▶ Play
            </button>
        </div>
    </div>

    <div class="test-section">
        <h2>Manual Controls</h2>
        <button class="test-button" onclick="showMiniPlayer()">Show Mini Player</button>
        <button class="test-button" onclick="hideMiniPlayer()">Hide Mini Player</button>
        <button class="test-button" onclick="testMiniPlayerAPI()">Test API</button>
    </div>

    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>

            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>

            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>

    <!-- Test Page Mini Player Integration -->
    <script>
        // Test Page Mini Player Integration
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for global mini player to be initialized
            setTimeout(() => {
                if (window.miniPlayer) {
                    console.log('🎵 Test page mini player integration ready');
                    showStatus('✅ Mini player integration loaded successfully', 'success');

                    // Listen for play buttons
                    document.addEventListener('click', (e) => {
                        const playBtn = e.target.closest('.track-play-btn');
                        if (playBtn && playBtn.dataset.preview) {
                            e.preventDefault();
                            e.stopPropagation();

                            const trackData = {
                                preview: playBtn.dataset.preview,
                                title: playBtn.dataset.title || 'Unknown Track',
                                artist: playBtn.dataset.artist || 'Unknown Artist',
                                artwork: playBtn.dataset.artwork || 'imgs/album-01.png'
                            };

                            // Use global mini player
                            window.miniPlayer.playTrack(trackData);
                            showStatus(`🎵 Playing: ${trackData.title} by ${trackData.artist}`, 'success');
                        }
                    });
                } else {
                    console.warn('⚠️ Global mini player not available');
                    showStatus('❌ Global mini player not available', 'error');
                }
            }, 100);
        });

        function showStatus(message, type = 'success') {
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            document.getElementById('status-results').appendChild(status);
            
            setTimeout(() => {
                if (status.parentNode) {
                    status.parentNode.removeChild(status);
                }
            }, 5000);
        }

        function checkMiniPlayerStatus() {
            const resultsDiv = document.getElementById('status-results');
            resultsDiv.innerHTML = '';

            const miniPlayerElement = document.getElementById('miniPlayer');
            const mainJsLoaded = typeof window.miniPlayer !== 'undefined';
            const utilsLoaded = typeof window.BansheeUtils !== 'undefined';

            showStatus(`Mini Player Element: ${miniPlayerElement ? '✅ Found' : '❌ Missing'}`, miniPlayerElement ? 'success' : 'error');
            showStatus(`Main.js Loaded: ${mainJsLoaded ? '✅ Yes' : '❌ No'}`, mainJsLoaded ? 'success' : 'error');
            showStatus(`Utils Loaded: ${utilsLoaded ? '✅ Yes' : '❌ No'}`, utilsLoaded ? 'success' : 'error');
            showStatus(`Global Mini Player API: ${window.miniPlayer ? '✅ Available' : '❌ Not Available'}`, window.miniPlayer ? 'success' : 'error');

            if (window.miniPlayer) {
                showStatus(`Mini Player Visible: ${window.miniPlayer.isVisible() ? '✅ Yes' : '❌ No'}`, 'warning');
            }
        }

        function showMiniPlayer() {
            if (window.miniPlayer) {
                window.miniPlayer.showMiniPlayer();
                showStatus('Mini player shown', 'success');
            } else {
                showStatus('Mini player API not available', 'error');
            }
        }

        function hideMiniPlayer() {
            if (window.miniPlayer) {
                window.miniPlayer.hideMiniPlayer();
                showStatus('Mini player hidden', 'success');
            } else {
                showStatus('Mini player API not available', 'error');
            }
        }

        function testMiniPlayerAPI() {
            if (window.miniPlayer) {
                const testTrack = {
                    preview: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
                    title: 'API Test Track',
                    artist: 'Test API',
                    artwork: 'imgs/album-01.png'
                };
                window.miniPlayer.playTrack(testTrack);
                showStatus('API test track started', 'success');
            } else {
                showStatus('Mini player API not available', 'error');
            }
        }
    </script>
</body>
</html>
