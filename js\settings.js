// Enhanced Settings Management System with Backend Integration
class SettingsManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.currentSettings = {};
        this.usernameCheckTimeout = null;
        this.init();
    }

    async init() {
        this.bindElements();
        this.bindEvents();
        await this.loadUserSettings();
    }

    bindElements() {
        this.accountSettingsForm = document.getElementById('accountSettingsForm');
        this.preferencesForm = document.getElementById('preferencesForm');
        this.deleteAccountButton = document.getElementById('delete-account');
        this.profileButton = document.querySelector('.profile-button');
        this.dropdown = document.querySelector('.dropdown');
        this.profilePictureInput = document.getElementById('profile-picture');
        this.profilePictureImg = document.getElementById('profile-picture-img');
        this.logoutLink = document.querySelector('.logout');
    }

    bindEvents() {
        // Profile dropdown toggle
        if (this.profileButton) {
            this.profileButton.addEventListener('click', () => {
                const expanded = this.profileButton.getAttribute('aria-expanded') === 'true';
                this.profileButton.setAttribute('aria-expanded', !expanded);
                this.dropdown.style.display = expanded ? 'none' : 'block';
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            if (this.profileButton && this.dropdown && 
                !this.profileButton.contains(event.target) && 
                !this.dropdown.contains(event.target)) {
                this.profileButton.setAttribute('aria-expanded', 'false');
                this.dropdown.style.display = 'none';
            }
        });

        // Form submissions
        if (this.accountSettingsForm) {
            this.accountSettingsForm.addEventListener('submit', (e) => this.handleAccountSubmit(e));
        }

        if (this.preferencesForm) {
            this.preferencesForm.addEventListener('submit', (e) => this.handlePreferencesSubmit(e));
        }

        // Real-time username validation
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.addEventListener('input', () => this.checkUsernameAvailability());
        }

        // Profile picture upload
        if (this.profilePictureInput) {
            this.profilePictureInput.addEventListener('change', (e) => this.handleProfilePictureUpload(e));
        }

        // Delete account
        if (this.deleteAccountButton) {
            this.deleteAccountButton.addEventListener('click', () => this.handleDeleteAccount());
        }

        // Logout
        if (this.logoutLink) {
            this.logoutLink.addEventListener('click', () => this.handleLogout());
        }
    }

    // Backend connectivity methods
    async loadUserSettings() {
        try {
            console.log('⚙️ Loading user settings from backend...');
            this.showLoadingState();

            const response = await fetch(`${this.apiBase}/settings`);
            
            if (response.ok) {
                const result = await response.json();
                this.currentSettings = result.settings;
                this.populateSettings(this.currentSettings);
                console.log('✅ Settings loaded successfully!');
            } else {
                throw new Error('Failed to load settings');
            }
        } catch (error) {
            console.error('❌ Error loading settings:', error);
            this.loadFallbackSettings();
            this.showMessage('Using demo settings - server unavailable', 'warning');
        } finally {
            this.hideLoadingState();
        }
    }

    loadFallbackSettings() {
        // Fallback settings from localStorage or defaults
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        this.currentSettings = {
            username: user.username || 'musiclover',
            email: user.email || '<EMAIL>',
            theme: user.theme || 'dark',
            language: 'en',
            audioQuality: 'high',
            autoplay: true,
            notifications: true,
            emailNotifications: true,
            profilePicture: 'imgs/profile-icon-B.png'
        };
        this.populateSettings(this.currentSettings);
    }

    async handleAccountSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(this.accountSettingsForm);
        const accountData = {
            username: formData.get('username'),
            email: formData.get('email'),
            currentPassword: formData.get('current-password'),
            newPassword: formData.get('password')
        };

        // Client-side validation
        if (accountData.newPassword && accountData.newPassword !== formData.get('confirm-password')) {
            this.showMessage('Passwords do not match. Please try again.', 'error');
            return;
        }

        try {
            this.showLoadingState();
            
            const response = await fetch(`${this.apiBase}/settings/account`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(accountData)
            });

            const result = await response.json();

            if (response.ok) {
                this.currentSettings = { ...this.currentSettings, ...result.settings };
                this.showMessage(result.message || 'Account settings updated successfully!', 'success');
                
                // Clear password fields
                this.accountSettingsForm.querySelectorAll('input[type="password"]').forEach(input => {
                    input.value = '';
                });
            } else {
                if (result.field) {
                    this.showFieldError(result.field, result.error);
                } else {
                    this.showMessage(result.error || 'Failed to update account settings', 'error');
                }
            }
        } catch (error) {
            console.error('Error updating account settings:', error);
            this.showMessage('Failed to update settings. Please check your connection.', 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    async handlePreferencesSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(this.preferencesForm);
        const preferences = {
            theme: formData.get('theme'),
            language: formData.get('language'),
            audioQuality: formData.get('quality'),
            autoplay: formData.get('autoplay') === 'on',
            notifications: formData.get('notifications') === 'on'
        };

        try {
            this.showLoadingState();
            
            const response = await fetch(`${this.apiBase}/settings/preferences`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(preferences)
            });

            const result = await response.json();

            if (response.ok) {
                this.currentSettings = { ...this.currentSettings, ...result.settings };
                this.showMessage(result.message || 'Preferences updated successfully!', 'success');
                
                // Apply theme change immediately
                if (preferences.theme !== this.currentSettings.theme) {
                    this.applyTheme(preferences.theme);
                }
            } else {
                this.showMessage(result.error || 'Failed to update preferences', 'error');
            }
        } catch (error) {
            console.error('Error updating preferences:', error);
            this.showMessage('Failed to update preferences. Please check your connection.', 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    // Username availability checking
    async checkUsernameAvailability() {
        const usernameInput = document.getElementById('username');
        const username = usernameInput.value.trim();
        
        if (!username || username.length < 3 || username === this.currentSettings.username) {
            this.clearUsernameIndicator();
            return;
        }

        // Clear previous timeout
        if (this.usernameCheckTimeout) {
            clearTimeout(this.usernameCheckTimeout);
        }

        // Debounce the API call
        this.usernameCheckTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`${this.apiBase}/settings/check-username`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username })
                });

                if (response.ok) {
                    const result = await response.json();
                    this.updateUsernameAvailability(result.available, result.message);
                }
            } catch (error) {
                console.error('Error checking username availability:', error);
            }
        }, 500);
    }

    updateUsernameAvailability(available, message) {
        const usernameInput = document.getElementById('username');
        const formGroup = usernameInput.closest('.settings-group');
        
        this.clearUsernameIndicator();

        const indicator = document.createElement('div');
        indicator.className = `username-availability ${available ? 'available' : 'unavailable'}`;
        indicator.innerHTML = `
            <i class="fas ${available ? 'fa-check-circle' : 'fa-times-circle'}"></i>
            ${message}
        `;
        formGroup.appendChild(indicator);
    }

    clearUsernameIndicator() {
        const existingIndicator = document.querySelector('.username-availability');
        if (existingIndicator) {
            existingIndicator.remove();
        }
    }

    async handleProfilePictureUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file
        if (!file.type.startsWith('image/')) {
            this.showMessage('Please select a valid image file', 'error');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            this.showMessage('Image file must be smaller than 5MB', 'error');
            return;
        }

        try {
            // Create preview
            const reader = new FileReader();
            reader.onload = (e) => {
                this.profilePictureImg.src = e.target.result;
            };
            reader.readAsDataURL(file);

            // In real app, would upload to server
            const response = await fetch(`${this.apiBase}/settings/profile-picture`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ profilePicture: 'uploaded-image-url' })
            });

            if (response.ok) {
                this.showMessage('Profile picture updated successfully!', 'success');
            }
        } catch (error) {
            console.error('Error uploading profile picture:', error);
            this.showMessage('Failed to upload profile picture', 'error');
        }
    }
}

    async handleDeleteAccount() {
        const password = prompt('Please enter your password to confirm account deletion:');
        if (!password) return;

        if (!confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
            return;
        }

        try {
            this.showLoadingState();

            const response = await fetch(`${this.apiBase}/settings/account`, {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ confirmPassword: password })
            });

            const result = await response.json();

            if (response.ok) {
                this.showMessage('Account deleted successfully. Redirecting...', 'success');
                localStorage.clear();
                sessionStorage.clear();
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                this.showMessage(result.error || 'Failed to delete account', 'error');
            }
        } catch (error) {
            console.error('Error deleting account:', error);
            this.showMessage('Failed to delete account. Please try again.', 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    async handleLogout() {
        try {
            this.showLoadingState();

            // Clear local storage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            sessionStorage.clear();

            this.showMessage('Logged out successfully. Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
        } catch (error) {
            console.error('Logout error:', error);
            this.showMessage('An error occurred during logout', 'error');
        } finally {
            this.hideLoadingState();
        }
    }

    // UI utility methods
    populateSettings(settings) {
        // Account settings
        const usernameInput = document.getElementById('username');
        const emailInput = document.getElementById('email');

        if (usernameInput) usernameInput.value = settings.username || '';
        if (emailInput) emailInput.value = settings.email || '';

        // Profile picture
        if (this.profilePictureImg && settings.profilePicture) {
            this.profilePictureImg.src = settings.profilePicture;
        }

        // Preferences
        const themeSelect = document.getElementById('theme');
        const languageSelect = document.getElementById('language');
        const qualitySelect = document.getElementById('quality');
        const autoplayCheckbox = document.getElementById('autoplay');
        const notificationsCheckbox = document.getElementById('notifications');

        if (themeSelect) themeSelect.value = settings.theme || 'dark';
        if (languageSelect) languageSelect.value = settings.language || 'en';
        if (qualitySelect) qualitySelect.value = settings.audioQuality || 'high';
        if (autoplayCheckbox) autoplayCheckbox.checked = settings.autoplay !== false;
        if (notificationsCheckbox) notificationsCheckbox.checked = settings.notifications !== false;
    }

    showFieldError(fieldName, message) {
        const field = document.getElementById(fieldName);
        if (!field) return;

        const formGroup = field.closest('.settings-group');
        const existingError = formGroup.querySelector('.field-error');

        if (existingError) {
            existingError.remove();
        }

        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        formGroup.appendChild(errorDiv);

        field.focus();

        // Remove error after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.settings-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'settings-message';
            document.querySelector('main').prepend(messageEl);
        }

        messageEl.className = `settings-message ${type}`;
        messageEl.innerHTML = `
            <i class="fas ${this.getMessageIcon(type)}"></i>
            ${message}
        `;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    getMessageIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-times-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    showLoadingState() {
        const loader = document.createElement('div');
        loader.id = 'settings-loader';
        loader.className = 'settings-loader';
        loader.innerHTML = `
            <div class="loader-spinner"></div>
            <p>Updating settings...</p>
        `;
        document.body.appendChild(loader);
    }

    hideLoadingState() {
        const loader = document.getElementById('settings-loader');
        if (loader) {
            loader.remove();
        }
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
    }
}

document.addEventListener('DOMContentLoaded', async () => {
    window.settingsManager = new SettingsManager();
});
