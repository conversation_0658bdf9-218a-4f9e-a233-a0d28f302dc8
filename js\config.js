// Banshee Music Configuration
// This file determines whether to use backend server or GitHub Pages mode

const BansheeConfig = {
    // GitHub Pages mode - set to true for hosting on GitHub Pages
    githubPagesMode: true,
    
    // Backend API URL (only used when githubPagesMode is false)
    apiBaseUrl: 'http://localhost:3001/api',
    
    // External API configurations
    apis: {
        deezer: {
            enabled: true,
            baseUrl: 'https://corsproxy.io/?https://api.deezer.com'
        },
        jamendo: {
            enabled: true,
            baseUrl: 'https://api.jamendo.com/v3.0',
            clientId: 'YOUR_JAMENDO_CLIENT_ID' // Replace with actual client ID
        }
    },
    
    // Demo data for GitHub Pages mode
    demo: {
        enabled: true,
        fallbackToDemo: true // Use demo data when APIs fail
    },
    
    // Feature flags
    features: {
        localStorageLibrary: true, // Use localStorage for playlists/likes
        hybridMusicService: true,  // Use hybrid Jamendo + <PERSON>zer
        offlineMode: true          // Work without backend
    },
    
    // GitHub Pages specific settings
    githubPages: {
        repository: 'bansheeblast', // Your GitHub repo name
        username: 'yourusername',   // Your GitHub username
        branch: 'main'              // Branch to deploy from
    }
};

// Auto-detect environment
BansheeConfig.isGitHubPages = window.location.hostname.includes('github.io');
BansheeConfig.isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

// Auto-configure based on environment
if (BansheeConfig.isGitHubPages) {
    BansheeConfig.githubPagesMode = true;
    console.log('🌐 Detected GitHub Pages environment - using offline mode');
} else if (BansheeConfig.isLocalhost) {
    // Keep current setting for localhost
    console.log(`🏠 Localhost detected - GitHub Pages mode: ${BansheeConfig.githubPagesMode}`);
}

// Make config globally available
window.BansheeConfig = BansheeConfig;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BansheeConfig;
}
