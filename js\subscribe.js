// Enhanced Subscribe Page Logic with Backend Integration
class SubscriptionManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.plans = {};
        this.init();
    }

    async init() {
        await this.loadPlans();
        this.bindEvents();
        this.setupPlanCardAnimations();
    }

    async loadPlans() {
        try {
            console.log('📋 Loading subscription plans from backend...');
            
            const response = await fetch(`${this.apiBase}/subscriptions/plans`);
            
            if (response.ok) {
                const result = await response.json();
                this.plans = result.plans.reduce((acc, plan) => {
                    acc[plan.id] = plan;
                    return acc;
                }, {});
                
                this.updatePlanDisplay();
                console.log('✅ Plans loaded successfully!');
            } else {
                throw new Error('Failed to load plans');
            }
        } catch (error) {
            console.error('❌ Error loading plans:', error);
            this.loadFallbackPlans();
            this.showToast('Using demo plans - server unavailable', 'warning');
        }
    }

    loadFallbackPlans() {
        this.plans = {
            free: {
                id: 'free',
                name: 'Free Account',
                price: 0.00,
                interval: 'month',
                features: [
                    'Enjoy Banshee with occasional ads',
                    'Basic audio quality (128kbps)',
                    'Limited skips (6 per hour)',
                    'Mobile app access'
                ]
            },
            premium: {
                id: 'premium',
                name: 'Premium',
                price: 2.99,
                interval: 'month',
                features: [
                    'Ad-free listening experience',
                    'High-quality audio (320kbps)',
                    'Unlimited skips',
                    'Offline mode',
                    'Cross-platform sync',
                    'Exclusive content access'
                ]
            },
            creator: {
                id: 'creator',
                name: 'Creator',
                price: 4.99,
                interval: 'month',
                features: [
                    'All Premium features included',
                    'Upload unlimited tracks',
                    'Advanced analytics dashboard',
                    'Promotional tools',
                    'Direct fan engagement',
                    'Custom artist profile'
                ]
            }
        };
        this.updatePlanDisplay();
    }

    updatePlanDisplay() {
        // Update plan prices and features from backend data
        Object.keys(this.plans).forEach(planId => {
            const plan = this.plans[planId];
            const planCard = document.querySelector(`[data-plan="${planId}"]`)?.closest('.plan-card');
            
            if (planCard) {
                const priceElement = planCard.querySelector('.amount');
                const featuresElement = planCard.querySelector('.features-list');
                
                if (priceElement) {
                    priceElement.textContent = plan.price.toFixed(2);
                }
                
                if (featuresElement && plan.features) {
                    featuresElement.innerHTML = plan.features.map(feature => 
                        `<li><i class="fas fa-check"></i> ${feature}</li>`
                    ).join('');
                }
            }
        });
    }

    bindEvents() {
        const planButtons = document.querySelectorAll('.plan-card .button');
        planButtons.forEach(button => {
            button.addEventListener('click', (e) => this.handlePlanSelection(e));
        });
    }

    async handlePlanSelection(event) {
        const button = event.target;
        const planType = button.getAttribute('data-plan');
        const plan = this.plans[planType];

        if (!plan) {
            this.showToast('Plan not found. Please try again.', 'error');
            return;
        }

        // Show loading state
        const originalText = button.textContent;
        button.textContent = 'Processing...';
        button.disabled = true;
        this.showLoader();

        try {
            // Create subscription via backend
            const response = await fetch(`${this.apiBase}/subscriptions`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    planId: planType,
                    userId: this.getCurrentUserId()
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                // Save subscription and plan data
                sessionStorage.setItem('selectedPlan', JSON.stringify(plan));
                sessionStorage.setItem('subscriptionId', result.subscription.id);
                
                this.hideLoader();
                this.showToast(result.message || `${plan.name} selected successfully!`, 'success');

                setTimeout(() => {
                    if (planType === 'free') {
                        // Free plan is activated immediately
                        window.location.href = 'index.html?welcome=true&plan=free';
                    } else {
                        // Paid plans need payment processing
                        window.location.href = 'payment.html';
                    }
                }, 2000);
            } else {
                throw new Error(result.message || 'Failed to create subscription');
            }
        } catch (error) {
            console.error('Subscription creation error:', error);
            this.hideLoader();
            
            // Fallback to sessionStorage only
            sessionStorage.setItem('selectedPlan', JSON.stringify(plan));
            this.showToast(`${plan.name} selected (offline mode)`, 'success');
            
            setTimeout(() => {
                if (planType === 'free') {
                    window.location.href = 'index.html?welcome=true';
                } else {
                    window.location.href = 'payment.html';
                }
            }, 2000);
        } finally {
            // Reset button state
            button.textContent = originalText;
            button.disabled = false;
        }
    }

    getCurrentUserId() {
        // Get user ID from stored user data
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (userJson) {
            const user = JSON.parse(userJson);
            return user.id;
        }
        return 1; // Default user ID for demo
    }

    setupPlanCardAnimations() {
        // Existing animation logic
        const planCards = document.querySelectorAll('.plan-card');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        planCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    showLoader() {
        const loader = document.createElement('div');
        loader.id = 'subscription-loader';
        loader.className = 'subscription-loader';
        loader.innerHTML = `
            <div class="loader-spinner"></div>
            <p>Processing subscription...</p>
        `;
        loader.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 10000;
        `;
        document.body.appendChild(loader);
    }

    hideLoader() {
        const loader = document.getElementById('subscription-loader');
        if (loader) {
            loader.remove();
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        `;
        
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '1rem 1.5rem',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '600',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            minWidth: '300px',
            background: type === 'success' 
                ? 'linear-gradient(135deg, #4CAF50, #45a049)'
                : type === 'error'
                ? 'linear-gradient(135deg, #f44336, #d32f2f)'
                : type === 'warning'
                ? 'linear-gradient(135deg, #ff9800, #f57c00)'
                : 'linear-gradient(135deg, #00E0FF, #FF006E)',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
        });

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 4000);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    window.subscriptionManager = new SubscriptionManager();
});
