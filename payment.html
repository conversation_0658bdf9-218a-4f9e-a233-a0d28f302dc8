<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/payment.css">
    <title>Payment | Banshee Music</title>
</head>


<body>

    <h1 class="welcome-header">Complete Your Payment</h1>

    <div class="subscription-progress">
        <div class="progress-step completed">
            <div class="step-icon"><i class="fas fa-check"></i></div>
            <span class="step-text">Choose Plan</span>
        </div>
        <div class="progress-step active">
            <div class="step-icon">2</div>
            <span class="step-text">Payment</span>
        </div>
        <div class="progress-step">
            <div class="step-icon">3</div>
            <span class="step-text">Confirmation</span>
        </div>
    </div>

    <div class="payment-container">
        <div class="plan-summary">
            <h2>Order Summary</h2>
            <div class="selected-plan">
                <div class="plan-info">
                    <span class="plan-name" id="selectedPlanName">Premium Plan</span>
                    <span class="plan-price" id="selectedPlanPrice">$2.99/month</span>
                </div>
                <a href="subscription.html" class="change-plan">Change Plan</a>
            </div>
            <ul class="plan-features">
                <li>
                    <i class="fas fa-music"></i>
                    <span>Ad-free music streaming</span>
                </li>
                <li>
                    <i class="fas fa-download"></i>
                    <span>Offline listening</span>
                </li>
            </ul>
        </div>

        <div class="payment-details">
            <h2>Payment Method</h2>
            <div class="payment-methods">
                <button type="button" class="payment-method active">
                    <i class="fas fa-credit-card"></i>
                    <span>Credit Card</span>
                </button>
                <button type="button" class="payment-method">
                    <i class="fab fa-paypal"></i>
                    <span>PayPal</span>
                </button>
            </div>

            <form id="paymentForm" class="card-payment-form">
                <div class="input-group">
                    <label for="card-name">Name on Card</label>
                    <input type="text" id="card-name" name="card-name"
                           placeholder="Enter cardholder name" required>
                </div>

                <div class="input-group card-number-group">
                    <label for="card-number">Card Number</label>
                    <div class="card-input-wrapper">
                        <input type="text" id="card-number" name="card-number"
                               placeholder="1234 5678 9012 3456" required>
                        <i class="card-type-icon fab fa-cc-generic"></i>
                    </div>
                </div>

                <div class="input-row">
                    <div class="input-group">
                        <label for="expiry-date">Expiry Date</label>
                        <input type="text" id="expiry-date" name="expiry-date"
                               placeholder="MM/YY" required>
                    </div>
                    <div class="input-group">
                        <label for="cvc">CVC</label>
                        <div class="cvc-input-wrapper">
                            <input type="text" id="cvc" name="cvc"
                                   placeholder="123" required>
                            <div class="cvc-tooltip">
                                <i class="fas fa-question-circle"></i>
                                <span class="tooltip-text">3-digit code on back of card</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="billing-address">
                    <h3>Billing Address</h3>
                    <div class="input-group">
                        <label for="country">Country</label>
                        <select id="country" name="country" required>
                            <option value="">Select Country</option>
                            <option value="US">United States</option>
                            <option value="CA">Canada</option>
                            <option value="GB">United Kingdom</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="zip">ZIP/Postal Code</label>
                        <input type="text" id="zip" name="zip" required>
                    </div>
                </div>

                <div class="terms-group">
                    <label class="terms-label">
                        <input type="checkbox" required>
                        <span>I agree to the <a href="#" class="terms-link">Terms & Conditions</a> and
                        <a href="#" class="terms-link">Privacy Policy</a></span>
                    </label>
                </div>

                <div class="secure-payment">
                    <i class="fas fa-lock"></i>
                    <span>Secure payment powered by Stripe</span>
                </div>

                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal</span>
                        <span id="subtotal">$2.99</span>
                    </div>
                    <div class="total-row">
                        <span>Tax</span>
                        <span id="tax">$0.00</span>
                    </div>
                    <div class="total-row total">
                        <span>Total</span>
                        <span id="total">$2.99</span>
                    </div>
                </div>

                <div class="button-group">
                    <a href="subscription.html" class="back-button">
                        <i class="fas fa-arrow-left"></i>
                        Back
                    </a>
                    <button type="submit" class="payment-button">
                        <span class="button-text">Pay Now</span>
                        <span class="button-price">$2.99</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

   
    <!-- Payment JS -->
    <script src="js/payment.js"></script>
</body>
</html>