:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    line-height: 1.6;
}

/* Container styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}


/* Welcome header styling */
.welcome-header {
    font-size: 2.5em;
    margin-top: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    text-shadow: 0 2px 16px rgba(0,224,255,0.18), 0 2px 24px rgba(255,0,110,0.12);
    text-align: center;
}

/* Card Base Styles */
.card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0 24px 6px var(--neon-blue), 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--neon-blue);
}

.card-content {
    padding: 1.5rem;
}

/* Button Base Styles */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-button);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-button-hover);
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
}

.button:active {
    transform: scale(0.98);
}

/* Main container styles */
.subscription-section {
    padding: 2rem 0;
}

/* Subscription plans container */
.subscription-plans {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

/* Plan card styles - using card.css as base */
.plan-card {
    width: 320px;
    margin: 0 auto;
}

/* Featured plan styling */
.card.plan-card.featured {
    background: linear-gradient(
        135deg,
        rgba(var(--electric-violet-rgb), 0.15) 0%,
        rgba(var(--neon-blue-rgb), 0.15) 100%
    );
    border: 1px solid rgba(var(--neon-blue-rgb), 0.2);
    box-shadow:
        0 8px 32px rgba(var(--neon-blue-rgb), 0.2),
        0 0 20px rgba(var(--neon-blue-rgb), 0.1);
    position: relative;
    z-index: 2;
}

.card.plan-card.featured:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 16px 48px rgba(var(--neon-blue-rgb), 0.25),
        0 0 30px rgba(var(--neon-blue-rgb), 0.15);
}

/* Popular tag for featured plan */
.popular-tag {
    position: absolute;
    top: 12px;
    right: 0;
    background: var(--gradient-primary);
    color: white;
    padding: 6px 15px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 3;
    text-align: center;
    letter-spacing: 0.5px; /* Improve readability */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Add subtle text shadow for better contrast */
    border-radius: 4px 0 0 4px; /* Rounded corners on left side */
}

/* Plan header */
.plan-header {
    padding: 2rem 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden; /* Ensure content inside doesn't overflow */
}

.plan-header h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

/* Price styling */
.price {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-bottom: 1rem;
}

.price .currency {
    font-size: 1.2rem;
    opacity: 0.8;
    align-self: flex-start;
    margin-top: 0.5rem;
}

.price .amount {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--neon-blue);
    line-height: 1;
}

.price .period {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: normal;
    align-self: flex-end;
    margin-bottom: 0.5rem;
}

/* Plan content */
.plan-content {
    padding: 1.5rem;
    overflow: hidden; /* Ensure content inside doesn't overflow */
}

/* Features list */
.features-list {
    margin: 0 0 1.5rem;
    padding: 0;
    list-style: none;
    text-align: left;
}

.features-list li {
    margin: 0.8rem 0;
    padding-left: 2rem;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

.features-list li i {
    position: absolute;
    left: 0;
    color: var(--neon-blue);
    font-size: 0.9rem;
}

.card.plan-card.featured .features-list li i {
    color: var(--cosmic-pink);
}

/* Selected plan styling */
.plan-card.selected {
    border: 2px solid var(--neon-blue);
    box-shadow: 0 0 32px 8px var(--neon-blue), 0 12px 40px rgba(0, 224, 255, 0.3);
    background: rgba(0, 224, 255, 0.05);
    transform: translateY(-5px) scale(1.02);
}

.plan-card.selected .button {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.4);
}

/* Button styling */
.plan-content .button {
    width: 100%;
    margin-top: 0.5rem;
    padding: 0.8rem 1.5rem;
}

/* Loading state for buttons */
.button.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress steps styling */
.subscription-progress {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin: 2rem 0;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(13, 17, 23, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(0, 224, 255, 0.1);
    transition: all 0.3s ease;
}

.progress-step.completed .step-number {
    background: var(--neon-blue);
    border-color: var(--neon-blue);
}

.progress-step.active .step-number {
    border-color: var(--neon-blue);
    color: var(--neon-blue);
}

.step-text {
    font-size: 0.9em;
    color: rgba(0, 224, 255, 0.7);
}

.progress-step.active .step-text {
    color: var(--neon-blue);
}

/* Responsive styles */
@media (max-width: 768px) {
    .welcome-header {
        font-size: 2.2rem;
        margin-bottom: 1.5rem;
    }

    .subscription-plans {
        gap: 2rem;
    }

    .plan-card {
        width: 300px;
    }

    .subscription-progress {
        gap: 1.5rem;
    }

    .features-list li {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .welcome-header {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .plan-card {
        width: 100%;
        max-width: 320px;
    }

    .subscription-progress {
        gap: 1rem;
    }

    .features-list li {
        font-size: 0.8rem;
        margin: 0.7rem 0;
    }
}

/* Footer Styles */
.site-footer {
    background: linear-gradient(135deg, rgba(19, 21, 26, 0.9), rgba(26, 29, 36, 0.9));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 4rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0;
}

.footer-nav {
    display: flex;
    gap: 2rem;
}

.footer-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: var(--neon-blue);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 6px 25px rgba(var(--neon-blue-rgb), 0.4);
}

/* Loader Styles */
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.loader {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--neon-blue);
    animation: spin 1s ease-in-out infinite;
}

.loader-container.show {
    display: flex;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    .footer-info {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-nav {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}
