<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/confirmation.css">
    <title>Subscription Confirmed | Banshee Music</title>
</head>


<body>

    <div class="confirmation-container">
        <div class="success-animation">
            <i class="fas fa-check-circle"></i>
        </div>

        <h1>Welcome to Banshee Premium!</h1>

        <div class="confirmation-details">
            <div class="subscription-info">
                <h2>Subscription Details</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">Plan</span>
                        <span class="value" id="planName">Premium</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Billing Cycle</span>
                        <span class="value" id="billingCycle">Monthly</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Next Billing Date</span>
                        <span class="value" id="nextBilling">March 14, 2024</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Amount</span>
                        <span class="value" id="amount">$2.99/month</span>
                    </div>
                </div>
            </div>

            <div class="features-preview">
                <h2>What's Next?</h2>
                <ul class="feature-list">
                    <li>
                        <i class="fas fa-music"></i>
                        <span>Enjoy ad-free music streaming</span>
                    </li>
                    <li>
                        <i class="fas fa-download"></i>
                        <span>Download unlimited songs for offline listening</span>
                    </li>
                    <li>
                        <i class="fas fa-headphones"></i>
                        <span>Experience HD audio quality</span>
                    </li>
                    <li>
                        <i class="fas fa-mobile-alt"></i>
                        <span>Listen on any device</span>
                    </li>
                </ul>
            </div>

            <div class="receipt-info">
                <p>A confirmation email has been sent to <span id="userEmail"><EMAIL></span></p>
                <button type="button" class="download-receipt">
                    <i class="fas fa-download"></i>
                    Download Receipt
                </button>
            </div>
        </div>

        <div class="action-buttons">
            <a href="player.html" class="primary-button">
                <i class="fas fa-play"></i>
                Start Listening
            </a>
            <a href="profile.html" class="secondary-button">
                <i class="fas fa-cog"></i>
                Manage Subscription
            </a>
        </div>
    </div>

    <!-- Mini Player -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-content">
            <div class="mini-player-info">
                <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
                <div class="mini-player-text">
                    <h4 id="miniPlayerTitle">Track Title</h4>
                    <p id="miniPlayerArtist">Artist Name</p>
                </div>
            </div>

            <div class="mini-player-controls">
                <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="mini-player-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="miniProgressFill"></div>
                </div>
                <div class="time-display">
                    <span id="miniCurrentTime">0:00</span>
                    <span id="miniDuration">3:45</span>
                </div>
            </div>

            <div class="mini-player-actions">
                <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                    <i class="fas fa-volume-up"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Shared utilities -->
    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
    <script src="js/confirmation.js"></script>

    <!-- Confirmation Page Mini Player Integration -->
    <script>
        // Confirmation Page Mini Player Integration
        document.addEventListener('DOMContentLoaded', () => {
            // Wait for global mini player to be initialized
            setTimeout(() => {
                if (window.miniPlayer) {
                    console.log('🎵 Confirmation page mini player integration ready');
                    // Note: Confirmation page typically won't have play buttons
                    // But mini player is available if needed
                } else {
                    console.warn('⚠️ Global mini player not available');
                }
            }, 100);
        });
    </script>
</body>
</html>