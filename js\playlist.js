// Playlist Page JavaScript
class PlaylistManager {
    constructor() {
        this.songs = [
            { id: 1, title: "Neon Dreams", artist: "Cyber Collective", album: "Digital Horizon", duration: "3:45", cover: "imgs/album-01.png" },
            { id: 2, title: "Electric Soul", artist: "Voltage", album: "Power Grid", duration: "4:12", cover: "imgs/album-02.png" },
            { id: 3, title: "Midnight Drive", artist: "Neon Cruiser", album: "Night Rider", duration: "5:23", cover: "imgs/album-03.png" },
            { id: 4, title: "Cyber Dawn", artist: "Digital Dreams", album: "Future Waves", duration: "3:58", cover: "imgs/album-04.png" },
            { id: 5, title: "Quantum Leap", artist: "Space Echo", album: "Cosmic Journey", duration: "4:33", cover: "imgs/album-05.png" },
            { id: 6, title: "Stellar Winds", artist: "Galaxy Drift", album: "Interstellar", duration: "6:01", cover: "imgs/album-06.png" },
            { id: 7, title: "Neon Pulse", artist: "Electric Nights", album: "City Lights", duration: "3:27", cover: "imgs/album-07.png" },
            { id: 8, title: "Digital Rain", artist: "Matrix Sound", album: "Code Stream", duration: "4:45", cover: "imgs/album-08.png" }
        ];
        this.currentSong = null;
        this.isPlaying = false;
        this.selectedSongs = new Set();

        this.init();
    }

    init() {
        this.renderSongs();
        this.setupEventListeners();
        this.setupPlaylistActions();
        this.setupModal();
    }

    renderSongs() {
        const container = document.getElementById('songs-container');
        if (!container) return;

        container.innerHTML = this.songs.map((song, index) => `
            <div class="song-item" data-song-id="${song.id}">
                <div class="song-number">
                    <input type="checkbox" class="song-checkbox" data-song-id="${song.id}">
                    <span class="track-number">${index + 1}</span>
                </div>
                <div class="song-title-wrapper">
                    <img src="${song.cover}" alt="${song.title}" class="song-cover">
                    <div>
                        <div class="song-title">${song.title}</div>
                    </div>
                </div>
                <div class="song-artist">${song.artist}</div>
                <div class="song-album">${song.album}</div>
                <div class="song-duration">${song.duration}</div>
                <div class="song-actions">
                    <button type="button" class="song-action-btn play-song" data-song-id="${song.id}" aria-label="Play ${song.title}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="song-action-btn add-to-playlist" data-song-id="${song.id}" aria-label="Add to playlist">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button type="button" class="song-action-btn more-options" data-song-id="${song.id}" aria-label="More options">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    setupEventListeners() {
        // Song play buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.play-song')) {
                const songId = parseInt(e.target.closest('.play-song').dataset.songId);
                this.playSong(songId);
            }
        });

        // Song selection
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('song-checkbox')) {
                this.handleSongSelection(e.target);
            }
        });

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.selectAllSongs(e.target.checked);
            });
        }

        // Sort dropdown
        const sortSelect = document.querySelector('.sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortSongs(e.target.value);
            });
        }

        // Selection actions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.selection-action')) {
                const action = e.target.closest('.selection-action').dataset.action;
                this.handleSelectionAction(action);
            }
        });
    }

    setupPlaylistActions() {
        // Play all button
        const playAllBtn = document.querySelector('.play-all');
        if (playAllBtn) {
            playAllBtn.addEventListener('click', () => {
                this.playAll();
            });
        }

        // Shuffle play button
        const shuffleBtn = document.querySelector('.shuffle-play');
        if (shuffleBtn) {
            shuffleBtn.addEventListener('click', () => {
                this.shufflePlay();
            });
        }

        // Share playlist button
        const shareBtn = document.querySelector('.share-playlist');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.showShareModal();
            });
        }

        // Editable playlist name
        const playlistName = document.getElementById('playlist-name');
        if (playlistName) {
            playlistName.addEventListener('blur', () => {
                this.savePlaylistName(playlistName.textContent);
            });
        }

        // Editable description
        const description = document.querySelector('.playlist-description');
        if (description) {
            description.addEventListener('blur', () => {
                this.saveDescription(description.textContent);
            });
        }
    }

    setupModal() {
        // Close modal
        document.addEventListener('click', (e) => {
            if (e.target.closest('.close-btn') || e.target.classList.contains('modal')) {
                this.hideShareModal();
            }
        });

        // Copy link button
        const copyBtn = document.querySelector('.copy-link-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                this.copyShareLink();
            });
        }

        // Share platform buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.share-platform-btn')) {
                const platform = e.target.closest('.share-platform-btn').classList[1];
                this.shareToSocial(platform);
            }
        });
    }

    playSong(songId) {
        const song = this.songs.find(s => s.id === songId);
        if (!song) return;

        // Update UI
        document.querySelectorAll('.song-item').forEach(item => {
            item.classList.remove('playing');
        });

        const songItem = document.querySelector(`[data-song-id="${songId}"]`);
        if (songItem) {
            songItem.classList.add('playing');
        }

        // Update now playing bar
        this.updateNowPlaying(song);

        this.currentSong = song;
        this.isPlaying = true;

        this.showToast(`Now playing: ${song.title}`, 'info');
    }

    updateNowPlaying(song) {
        const nowPlayingImg = document.querySelector('.now-playing-left img');
        const trackName = document.querySelector('.track-name');
        const artistName = document.querySelector('.artist-name');
        const playPauseBtn = document.querySelector('.play-pause');

        if (nowPlayingImg) nowPlayingImg.src = song.cover;
        if (trackName) trackName.textContent = song.title;
        if (artistName) artistName.textContent = song.artist;
        if (playPauseBtn) {
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
        }
    }

    handleSongSelection(checkbox) {
        const songId = parseInt(checkbox.dataset.songId);

        if (checkbox.checked) {
            this.selectedSongs.add(songId);
        } else {
            this.selectedSongs.delete(songId);
        }

        this.updateSelectionToolbar();
    }

    selectAllSongs(selectAll) {
        const checkboxes = document.querySelectorAll('.song-checkbox');

        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            const songId = parseInt(checkbox.dataset.songId);

            if (selectAll) {
                this.selectedSongs.add(songId);
            } else {
                this.selectedSongs.delete(songId);
            }
        });

        this.updateSelectionToolbar();
    }

    updateSelectionToolbar() {
        const toolbar = document.querySelector('.selection-toolbar');
        const selectedCount = document.getElementById('selected-count');

        if (toolbar && selectedCount) {
            if (this.selectedSongs.size > 0) {
                toolbar.hidden = false;
                selectedCount.textContent = this.selectedSongs.size;
            } else {
                toolbar.hidden = true;
            }
        }
    }

    handleSelectionAction(action) {
        const selectedArray = Array.from(this.selectedSongs);

        switch (action) {
            case 'play':
                if (selectedArray.length > 0) {
                    this.playSong(selectedArray[0]);
                    this.showToast(`Playing ${selectedArray.length} selected songs`, 'success');
                }
                break;
            case 'add':
                this.showToast(`Added ${selectedArray.length} songs to playlist`, 'success');
                break;
            case 'remove':
                this.removeSongs(selectedArray);
                break;
        }
    }

    removeSongs(songIds) {
        this.songs = this.songs.filter(song => !songIds.includes(song.id));
        this.selectedSongs.clear();
        this.renderSongs();
        this.updateSelectionToolbar();
        this.showToast(`Removed ${songIds.length} songs`, 'success');
    }

    sortSongs(sortBy) {
        switch (sortBy) {
            case 'title':
                this.songs.sort((a, b) => a.title.localeCompare(b.title));
                break;
            case 'artist':
                this.songs.sort((a, b) => a.artist.localeCompare(b.artist));
                break;
            case 'album':
                this.songs.sort((a, b) => a.album.localeCompare(b.album));
                break;
            default:
                // Keep original order for 'recent'
                break;
        }

        this.renderSongs();
        this.showToast(`Sorted by ${sortBy}`, 'info');
    }

    playAll() {
        if (this.songs.length > 0) {
            this.playSong(this.songs[0].id);
            this.showToast('Playing all songs', 'success');
        }
    }

    shufflePlay() {
        if (this.songs.length > 0) {
            const randomIndex = Math.floor(Math.random() * this.songs.length);
            this.playSong(this.songs[randomIndex].id);
            this.showToast('Shuffle play started', 'success');
        }
    }

    showShareModal() {
        const modal = document.getElementById('share-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    hideShareModal() {
        const modal = document.getElementById('share-modal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    copyShareLink() {
        const shareLink = document.getElementById('share-link');
        if (shareLink) {
            shareLink.select();
            document.execCommand('copy');
            this.showToast('Link copied to clipboard!', 'success');
        }
    }

    shareToSocial(platform) {
        const url = encodeURIComponent(window.location.href);
        const text = encodeURIComponent('Check out this awesome playlist on Banshee Music!');

        const shareUrls = {
            facebook: `https://www.facebook.com/sharer/sharer.php?u=${url}`,
            twitter: `https://twitter.com/intent/tweet?url=${url}&text=${text}`,
            whatsapp: `https://wa.me/?text=${text}%20${url}`
        };

        if (shareUrls[platform]) {
            window.open(shareUrls[platform], '_blank', 'width=600,height=400');
        }
    }

    savePlaylistName(name) {
        this.showToast('Playlist name saved', 'success');
    }

    saveDescription(description) {
        this.showToast('Description saved', 'success');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }
}

// Initialize playlist manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PlaylistManager();
});