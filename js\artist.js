// Enhanced Artist Page Management System with Backend Integration
class ArtistPageManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.currentArtist = null;
        this.artistName = this.getArtistFromURL() || 'The Weeknd';
        this.init();
    }

    async init() {
        // Load artist data from backend
        await this.loadArtistData();
    }

    getArtistFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('artist') || null;
    }

    async loadArtistData() {
        try {
            console.log(`🎵 Loading data for artist: ${this.artistName}`);

            const artistResponse = await fetch(`${this.apiBase}/artist/${encodeURIComponent(this.artistName)}`);

            if (artistResponse.ok) {
                this.currentArtist = await artistResponse.json();
                this.updateArtistInfo(this.currentArtist);
            }

            console.log('✅ Artist data loaded successfully!');
        } catch (error) {
            console.error('❌ Error loading artist data:', error);
        }
    }

    updateArtistInfo(artist) {
        // Update artist name
        const artistNameElement = document.querySelector('.artist-name h1');
        if (artistNameElement) {
            artistNameElement.innerHTML = `${artist.name} <span class="verified-badge" title="Verified Artist"><i class="fas fa-check-circle"></i></span>`;
        }

        // Update bio
        const artistBio = document.getElementById('artist-bio');
        if (artistBio) {
            artistBio.textContent = artist.bio;
        }

        // Update stats with animation
        const statNumbers = document.querySelectorAll('.stat-number');
        if (statNumbers.length >= 3) {
            this.animateStatNumber(statNumbers[0], artist.stats.monthlyListeners);
            this.animateStatNumber(statNumbers[1], artist.stats.followers);
            this.animateStatNumber(statNumbers[2], artist.stats.totalAlbums);
        }

        console.log('📊 Artist info updated:', artist);
    }

    animateStatNumber(element, targetValue) {
        // Extract number from string like "2.5M" -> 2.5
        const numericValue = parseFloat(targetValue.toString().replace(/[^\d.]/g, '')) || 0;
        const suffix = targetValue.toString().replace(/[\d.]/g, '');

        const startValue = 0;
        const duration = 2000; // 2 seconds
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = (startValue + (numericValue - startValue) * easeOutQuart).toFixed(1);

            element.textContent = currentValue + suffix;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }
}

// Initialize artist page manager
let artistPageManager;

// Add floating play button
function addFloatingPlayButton() {
    const floatingBtn = document.createElement('button');
    floatingBtn.className = 'floating-play-btn';
    floatingBtn.innerHTML = '<i class="fas fa-play"></i>';
    floatingBtn.setAttribute('aria-label', 'Play artist\'s top track');
    floatingBtn.onclick = () => {
        const firstTrack = document.querySelector('.track-item[data-preview]');
        if (firstTrack) {
            const playBtn = firstTrack.querySelector('.track-play-btn');
            if (playBtn) playBtn.click();
            firstTrack.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };
    document.body.appendChild(floatingBtn);
}

document.addEventListener('DOMContentLoaded', function () {
    // Initialize artist page manager with backend integration
    artistPageManager = new ArtistPageManager();

    // Add floating play button
    addFloatingPlayButton();
    
    const shareBtn = document.querySelector('.share-btn');
    const shareModal = document.getElementById('shareModal');
    const followBtn = document.querySelector('.follow-btn');
    const followText = followBtn ? followBtn.querySelector('.follow-text') : null;
    const playBtn = document.querySelector('.play-btn');
    const dropdownBtn = document.querySelector('.profile-button');
    const dropdownMenu = document.getElementById('dropdown-menu');
    const artistStats = document.querySelectorAll('.stat-number');
    const bio = document.getElementById('artist-bio');
    const readMoreBtn = document.querySelector('.read-more-btn');

    // Share modal open/close logic
    if (shareBtn && shareModal) {
        shareBtn.addEventListener('click', () => {
            shareModal.hidden = false;
        });
        shareModal.addEventListener('click', (e) => {
            if (e.target === shareModal) shareModal.hidden = true;
        });
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !shareModal.hidden) shareModal.hidden = true;
        });
        // Share/copy logic
        shareModal.querySelectorAll('.share-option').forEach(btn => {
            btn.addEventListener('click', () => {
                const url = window.location.href;
                if (btn.classList.contains('copy-link')) {
                    navigator.clipboard.writeText(url);
                    btn.textContent = 'Copied!';
                    setTimeout(() => btn.innerHTML = '<i class="fas fa-link"></i> Copy Link', 1500);
                } else {
                    let shareUrl = '';
                    if (btn.dataset.platform === 'facebook') shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                    if (btn.dataset.platform === 'twitter') shareUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
                    if (btn.dataset.platform === 'instagram') shareUrl = 'https://www.instagram.com/'; // Instagram doesn't support direct share
                    if (shareUrl) window.open(shareUrl, '_blank');
                }
            });
        });
    }

    // Follow button toggle
    if (followBtn && followText) {
        let following = localStorage.getItem('artist_following') === 'true';
        updateFollowBtn();
        followBtn.addEventListener('click', () => {
            following = !following;
            localStorage.setItem('artist_following', following);
            updateFollowBtn();
        });
        function updateFollowBtn() {
            followText.textContent = following ? 'Following' : 'Follow';
            followBtn.classList.toggle('following', following);
        }
    }

    // Play button demo (plays first audio preview in carousel if available)
    if (playBtn) {
        playBtn.addEventListener('click', () => {
            const firstTrack = document.querySelector('.track-item[data-preview]');
            if (firstTrack) {
                let audio = document.getElementById('artist-audio-preview');
                if (!audio) {
                    audio = document.createElement('audio');
                    audio.id = 'artist-audio-preview';
                    document.body.appendChild(audio);
                }
                audio.src = firstTrack.dataset.preview;
                audio.play();
                playBtn.classList.add('playing');
                audio.onended = () => playBtn.classList.remove('playing');
            }
        });
    }

    // Dropdown accessibility (toggle on click/focus)
    if (dropdownBtn && dropdownMenu) {
        dropdownBtn.addEventListener('click', () => {
            const expanded = dropdownBtn.getAttribute('aria-expanded') === 'true';
            dropdownBtn.setAttribute('aria-expanded', !expanded);
            dropdownMenu.style.display = expanded ? 'none' : 'block';
        });
        document.addEventListener('click', (e) => {
            if (!dropdownBtn.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownBtn.setAttribute('aria-expanded', 'false');
                dropdownMenu.style.display = 'none';
            }
        });
    }

    // Animate artist stats on scroll into view
    if (artistStats.length) {
        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) animateStat(entry.target);
            });
        }, { threshold: 0.7 });
        artistStats.forEach(stat => observer.observe(stat));
        function animateStat(el) {
            const target = parseInt(el.textContent.replace(/\D/g, ''));
            let count = 0;
            const step = Math.ceil(target / 60);
            const interval = setInterval(() => {
                count += step;
                if (count >= target) {
                    el.textContent = target.toLocaleString();
                    clearInterval(interval);
                } else {
                    el.textContent = count.toLocaleString();
                }
            }, 16);
        }
    }

    // Smooth transition for artist bio expansion
    if (bio && readMoreBtn) {
        const fullText = bio.innerHTML;
        const shortText = fullText.split('<br><br>')[0] + '...';
        let expanded = false;
        function updateBio() {
            bio.innerHTML = expanded ? fullText : shortText;
            readMoreBtn.textContent = expanded ? 'Show Less' : 'Read More';
            readMoreBtn.setAttribute('aria-expanded', expanded);
            const container = bio.parentElement;
            if (expanded) {
                container.style.maxHeight = container.scrollHeight + 'px';
                container.style.paddingBottom = '1em';
            } else {
                container.style.maxHeight = '4.5em';
                container.style.paddingBottom = '0.5em';
            }
        }
        updateBio();
        readMoreBtn.addEventListener('click', () => {
            expanded = !expanded;
            updateBio();
        });
    }

    // Gallery modal logic
    const galleryItems = document.querySelectorAll('.gallery-item');
    const galleryModal = document.getElementById('galleryModal');
    const galleryModalMedia = document.getElementById('galleryModalMedia');
    const closeGalleryModal = document.querySelector('.close-gallery-modal');
    if (galleryItems.length && galleryModal && galleryModalMedia && closeGalleryModal) {
        galleryItems.forEach(item => {
            item.addEventListener('click', () => openGalleryItem(item));
            item.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    openGalleryItem(item);
                }
            });
        });
        function openGalleryItem(item) {
            const img = item.querySelector('img');
            const video = item.querySelector('video');
            galleryModalMedia.innerHTML = '';
            let caption = '';
            if (img) {
                const modalImg = document.createElement('img');
                modalImg.src = img.src;
                modalImg.alt = img.alt;
                galleryModalMedia.appendChild(modalImg);
                caption = img.alt;
            } else if (video) {
                const modalVideo = document.createElement('video');
                modalVideo.src = video.src;
                modalVideo.controls = true;
                modalVideo.autoplay = true;
                modalVideo.poster = video.poster;
                galleryModalMedia.appendChild(modalVideo);
                caption = video.getAttribute('data-caption') || 'Video';
            }
            if (caption) {
                const captionDiv = document.createElement('div');
                captionDiv.className = 'gallery-caption';
                captionDiv.textContent = caption;
                galleryModalMedia.appendChild(captionDiv);
            }
            // Add close button inside modal
            if (!galleryModalMedia.querySelector('.gallery-close-btn')) {
                const closeBtn = document.createElement('button');
                closeBtn.className = 'gallery-close-btn';
                closeBtn.setAttribute('aria-label', 'Close gallery');
                closeBtn.innerHTML = '&times;';
                closeBtn.onclick = () => {
                    galleryModal.hidden = true;
                    galleryModalMedia.innerHTML = '';
                    if (lastFocusedGalleryItem) lastFocusedGalleryItem.focus();
                };
                galleryModalMedia.appendChild(closeBtn);
            }
            galleryModal.hidden = false;
            closeGalleryModal.focus();
            trapFocus(galleryModal);
        }
        function trapFocus(modal) {
            const focusableEls = modal.querySelectorAll('button, [tabindex]:not([tabindex="-1"])');
            const firstEl = focusableEls[0];
            const lastEl = focusableEls[focusableEls.length - 1];
            modal.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    if (e.shiftKey) {
                        if (document.activeElement === firstEl) {
                            e.preventDefault();
                            lastEl.focus();
                        }
                    } else {
                        if (document.activeElement === lastEl) {
                            e.preventDefault();
                            firstEl.focus();
                        }
                    }
                }
            });
        }
        // Gallery modal next/prev navigation
        galleryModal.addEventListener('keydown', (e) => {
            if (!galleryModal.hidden && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
                const items = Array.from(galleryItems);
                let idx = items.indexOf(lastFocusedGalleryItem);
                if (e.key === 'ArrowLeft') idx = (idx - 1 + items.length) % items.length;
                if (e.key === 'ArrowRight') idx = (idx + 1) % items.length;
                lastFocusedGalleryItem = items[idx];
                openGalleryItem(items[idx]);
            }
        });
        closeGalleryModal.addEventListener('click', () => {
            galleryModal.hidden = true;
            galleryModalMedia.innerHTML = '';
            if (lastFocusedGalleryItem) lastFocusedGalleryItem.focus();
        });
        closeGalleryModal.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                galleryModal.hidden = true;
                galleryModalMedia.innerHTML = '';
                if (lastFocusedGalleryItem) lastFocusedGalleryItem.focus();
            }
        });
        galleryModal.addEventListener('click', (e) => {
            if (e.target === galleryModal) {
                galleryModal.hidden = true;
                galleryModalMedia.innerHTML = '';
                if (lastFocusedGalleryItem) lastFocusedGalleryItem.focus();
            }
        });
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !galleryModal.hidden) {
                galleryModal.hidden = true;
                galleryModalMedia.innerHTML = '';
                if (lastFocusedGalleryItem) lastFocusedGalleryItem.focus();
            }
        });
    }

    // --- DEMO TRACKS SECTION REFINEMENT ---
    let lastFocusedTrack = null;
    function playTrack(trackItem) {
        if (currentTrack === trackItem && currentAudio && !currentAudio.paused) {
            currentAudio.pause();
            trackItem.classList.remove('playing');
            trackItem.setAttribute('aria-label', trackItem.getAttribute('aria-label').replace('Pause', 'Play'));
            return;
        }
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            if (currentTrack) {
                currentTrack.classList.remove('playing');
                currentTrack.removeAttribute('aria-current');
                currentTrack.setAttribute('aria-label', currentTrack.getAttribute('aria-label').replace('Pause', 'Play'));
                const progress = currentTrack.querySelector('.track-progress-bar');
                if (progress) progress.style.width = '0%';
            }
        }
        currentTrack = trackItem;
        let audio = document.getElementById('artist-audio-preview');
        if (!audio) {
            audio = document.createElement('audio');
            audio.id = 'artist-audio-preview';
            document.body.appendChild(audio);
        }
        currentAudio = audio;
        audio.src = trackItem.dataset.preview;
        audio.play();
        trackItem.classList.add('playing');
        trackItem.setAttribute('aria-current', 'true');
        trackItem.setAttribute('aria-label', trackItem.getAttribute('aria-label').replace('Play', 'Pause'));
        updateProgressBar(trackItem, audio);
        audio.onended = () => {
            trackItem.classList.remove('playing');
            trackItem.removeAttribute('aria-current');
            trackItem.setAttribute('aria-label', trackItem.getAttribute('aria-label').replace('Pause', 'Play'));
            const progress = trackItem.querySelector('.track-progress-bar');
            if (progress) progress.style.width = '0%';
            if (autoPlayEnabled) playNextTrack();
        };
    }
    trackItems.forEach(item => {
        const playBtn = item.querySelector('.track-play-btn');
        if (playBtn) {
            playBtn.addEventListener('click', e => {
                e.stopPropagation();
                playTrack(item);
            });
            playBtn.setAttribute('aria-pressed', 'false');
        }
        item.addEventListener('keydown', e => {
            if (e.key === 'Enter' || e.key === ' ') {
                playTrack(item);
            }
        });
        item.addEventListener('focus', () => { lastFocusedTrack = item; });
        item.addEventListener('click', () => playTrack(item));
    });
    // Auto-play button
    const autoPlayBtn = document.querySelector('.auto-play-btn');
    if (autoPlayBtn) {
        autoPlayBtn.addEventListener('click', () => {
            autoPlayEnabled = !autoPlayEnabled;
            autoPlayBtn.classList.toggle('active', autoPlayEnabled);
            autoPlayBtn.setAttribute('aria-pressed', autoPlayEnabled);
        });
    }
    // View All button scrolls to Demo Tracks section
    const viewAllBtn = document.querySelector('.view-all-btn');
    if (viewAllBtn) {
        viewAllBtn.addEventListener('click', () => {
            const carousel = document.querySelector('.track-carousel');
            if (carousel) carousel.scrollIntoView({ behavior: 'smooth' });
        });
    }

    // --- DEMO TRACKS SECTION ENHANCEMENTS ---
    function formatTime(sec) {
        if (isNaN(sec)) return '0:00';
        const m = Math.floor(sec / 60);
        const s = Math.floor(sec % 60);
        return m + ':' + (s < 10 ? '0' : '') + s;
    }
    function updateTrackTimes(trackItem, audio) {
        const current = trackItem.querySelector('.current-time');
        const total = trackItem.querySelector('.total-time');
        if (current) current.textContent = formatTime(audio.currentTime);
        if (total) total.textContent = formatTime(audio.duration);
    }
    function setLoading(trackItem, loading) {
        const loadingEl = trackItem.querySelector('.track-loading');
        if (loadingEl) loadingEl.hidden = !loading;
    }
    function setPlayPauseIcon(trackItem, playing) {
        const playIcon = trackItem.querySelector('.fa-play');
        const pauseIcon = trackItem.querySelector('.fa-pause');
        if (playIcon && pauseIcon) {
            playIcon.style.display = playing ? 'none' : 'inline';
            pauseIcon.style.display = playing ? 'inline' : 'none';
        }
    }
    function setMuteIcon(btn, muted) {
        if (!btn) return;
        btn.innerHTML = muted ? '<i class="fas fa-volume-mute"></i>' : '<i class="fas fa-volume-up"></i>';
    }
    function playTrack(trackItem) {
        if (currentTrack === trackItem && currentAudio && !currentAudio.paused) {
            currentAudio.pause();
            setPlayPauseIcon(trackItem, false);
            trackItem.classList.remove('playing');
            trackItem.setAttribute('aria-label', trackItem.getAttribute('aria-label').replace('Pause', 'Play'));
            return;
        }
        if (currentAudio) {
            currentAudio.pause();
            currentAudio.currentTime = 0;
            if (currentTrack) {
                setPlayPauseIcon(currentTrack, false);
                currentTrack.classList.remove('playing');
                currentTrack.removeAttribute('aria-current');
                currentTrack.setAttribute('aria-label', currentTrack.getAttribute('aria-label').replace('Pause', 'Play'));
                const progress = currentTrack.querySelector('.track-progress-bar');
                if (progress) progress.style.width = '0%';
                updateTrackTimes(currentTrack, {currentTime:0, duration:currentAudio.duration||0});
            }
        }
        currentTrack = trackItem;
        let audio = document.getElementById('artist-audio-preview');
        if (!audio) {
            audio = document.createElement('audio');
            audio.id = 'artist-audio-preview';
            document.body.appendChild(audio);
        }
        currentAudio = audio;
        audio.src = trackItem.dataset.preview;
        setLoading(trackItem, true);
        audio.load();
        audio.oncanplay = () => {
            setLoading(trackItem, false);
            audio.play();
        };
        audio.onplaying = () => {
            setPlayPauseIcon(trackItem, true);
        };
        audio.onpause = () => {
            setPlayPauseIcon(trackItem, false);
        };
        trackItem.classList.add('playing');
        trackItem.setAttribute('aria-current', 'true');
        trackItem.setAttribute('aria-label', trackItem.getAttribute('aria-label').replace('Play', 'Pause'));
        updateProgressBar(trackItem, audio);
        audio.ontimeupdate = () => {
            updateTrackTimes(trackItem, audio);
        };
        audio.onended = () => {
            setPlayPauseIcon(trackItem, false);
            trackItem.classList.remove('playing');
            trackItem.removeAttribute('aria-current');
            trackItem.setAttribute('aria-label', trackItem.getAttribute('aria-label').replace('Pause', 'Play'));
            const progress = trackItem.querySelector('.track-progress-bar');
            if (progress) progress.style.width = '0%';
            updateTrackTimes(trackItem, {currentTime:0, duration:audio.duration||0});
            if (autoPlayEnabled) playNextTrack();
        };
        // Volume/mute
        const muteBtn = trackItem.querySelector('.track-mute-btn');
        if (muteBtn) {
            setMuteIcon(muteBtn, audio.muted);
            muteBtn.onclick = () => {
                audio.muted = !audio.muted;
                setMuteIcon(muteBtn, audio.muted);
            };
        }
        // Set initial times
        updateTrackTimes(trackItem, audio);
    }
    // Add pause icon to play button
    document.querySelectorAll('.track-play-btn').forEach(btn => {
        if (!btn.querySelector('.fa-pause')) {
            btn.insertAdjacentHTML('beforeend', '<i class="fas fa-pause" style="display:none"></i>');
        }
    });
    // Keyboard navigation between tracks
    document.querySelectorAll('.track-item').forEach((item, idx, arr) => {
        item.addEventListener('keydown', e => {
            if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                e.preventDefault();
                if (arr[idx+1]) arr[idx+1].focus();
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                e.preventDefault();
                if (arr[idx-1]) arr[idx-1].focus();
            }
        });
    });
    // --- GALLERY SECTION ENHANCEMENTS ---
    // Touch swipe for modal
    let startX = 0;
    galleryModal.addEventListener('touchstart', e => {
        if (e.touches.length === 1) startX = e.touches[0].clientX;
    });
    galleryModal.addEventListener('touchend', e => {
        if (e.changedTouches.length === 1) {
            const dx = e.changedTouches[0].clientX - startX;
            if (Math.abs(dx) > 50) {
                const items = Array.from(galleryItems);
                let idx = items.indexOf(lastFocusedGalleryItem);
                if (dx < 0) idx = (idx + 1) % items.length;
                else idx = (idx - 1 + items.length) % items.length;
                lastFocusedGalleryItem = items[idx];
                openGalleryItem(items[idx]);
            }
        }
    });
    // Add download button in modal
    function addDownloadButton(mediaElement, src, filename) {
        const downloadBtn = document.createElement('a');
        downloadBtn.className = 'gallery-download-btn';
        downloadBtn.href = src;
        downloadBtn.download = filename;
        downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download';
        downloadBtn.setAttribute('aria-label', `Download ${filename}`);
        galleryModalMedia.appendChild(downloadBtn);
    }
    function openGalleryItem(item) {
        const img = item.querySelector('img');
        const video = item.querySelector('video');
        galleryModalMedia.innerHTML = '';
        let caption = '';
        if (img) {
            const modalImg = document.createElement('img');
            modalImg.src = img.src;
            modalImg.alt = img.alt;
            modalImg.loading = 'lazy';
            galleryModalMedia.appendChild(modalImg);
            caption = img.alt;
            addDownloadButton(modalImg, img.src, img.alt.replace(/\s+/g,'_'));
        } else if (video) {
            const modalVideo = document.createElement('video');
            modalVideo.src = video.src;
            modalVideo.controls = true;
            modalVideo.autoplay = true;
            modalVideo.poster = video.poster;
            modalVideo.setAttribute('loading', 'lazy');
            galleryModalMedia.appendChild(modalVideo);
            caption = video.getAttribute('data-caption') || 'Video';
            addDownloadButton(modalVideo, video.src, caption.replace(/\s+/g,'_'));
        }
        if (caption) {
            const captionDiv = document.createElement('div');
            captionDiv.className = 'gallery-caption';
            captionDiv.textContent = caption;
            galleryModalMedia.appendChild(captionDiv);
        }
        // Add close button inside modal
        if (!galleryModalMedia.querySelector('.gallery-close-btn')) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'gallery-close-btn';
            closeBtn.setAttribute('aria-label', 'Close gallery');
            closeBtn.innerHTML = '&times;';
            closeBtn.onclick = () => {
                galleryModal.hidden = true;
                galleryModalMedia.innerHTML = '';
                if (lastFocusedGalleryItem) lastFocusedGalleryItem.focus();
            };
            galleryModalMedia.appendChild(closeBtn);
        }
        galleryModal.hidden = false;
        closeGalleryModal.focus();
        trapFocus(galleryModal);
    }
    // Lazy loading for gallery images/videos
    document.querySelectorAll('.gallery-img').forEach(img => img.loading = 'lazy');
    document.querySelectorAll('.gallery-video').forEach(vid => vid.setAttribute('loading', 'lazy'));

    // --- FEATURED ALBUMS: Album details modal logic (placeholder) ---
    document.querySelectorAll('.album-card').forEach(card => {
        card.addEventListener('click', () => {
            const modal = document.getElementById('albumDetailsModal');
            if (modal) {
                modal.innerHTML = `<h2 id="albumDetailsTitle">${card.querySelector('.album-title').textContent}</h2><p>Tracklist and details coming soon.</p><button onclick="this.parentElement.hidden=true">Close</button>`;
                modal.hidden = false;
                modal.focus();
            }
        });
    });
    // --- EVENTS: Ticket status and calendar integration (placeholder) ---
    document.querySelectorAll('.event-ticket').forEach(btn => {
        btn.addEventListener('click', () => {
            document.getElementById('aria-live-region').textContent = 'Ticket link clicked.';
        });
    });
    // --- SOCIAL SHARING: WhatsApp/Telegram ---
    const shareOptions = document.querySelector('.share-options');
    if (shareOptions && !shareOptions.querySelector('.share-option[data-platform="whatsapp"]')) {
        const waBtn = document.createElement('button');
        waBtn.type = 'button';
        waBtn.className = 'share-option';
        waBtn.setAttribute('data-platform', 'whatsapp');
        waBtn.setAttribute('aria-label', 'Share on WhatsApp');
        waBtn.innerHTML = '<i class="fab fa-whatsapp"></i> WhatsApp';
        waBtn.onclick = () => window.open(`https://wa.me/?text=${encodeURIComponent(window.location.href)}`, '_blank');
        shareOptions.appendChild(waBtn);
        const tgBtn = document.createElement('button');
        tgBtn.type = 'button';
        tgBtn.className = 'share-option';
        tgBtn.setAttribute('data-platform', 'telegram');
        tgBtn.setAttribute('aria-label', 'Share on Telegram');
        tgBtn.innerHTML = '<i class="fab fa-telegram"></i> Telegram';
        tgBtn.onclick = () => window.open(`https://t.me/share/url?url=${encodeURIComponent(window.location.href)}`, '_blank');
        shareOptions.appendChild(tgBtn);
    }
    // --- FAN WALL & RATING (placeholder) ---
    // For future: dynamically load comments and ratings from API

    // Add keyboard navigation for track carousel
    const trackCarousel = document.querySelector('.track-carousel');
    if (trackCarousel) {
        trackCarousel.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight') {
                trackCarousel.flickity.next();
            } else if (e.key === 'ArrowLeft') {
                trackCarousel.flickity.previous();
            }
        });
    }

    // --- MINI PLAYER LOGIC ---
    const miniPlayer = document.getElementById('mini-player');
    const miniImg = document.getElementById('mini-player-img');
    const miniTitle = document.getElementById('mini-player-title');
    const miniMeta = document.getElementById('mini-player-meta');
    const miniPlay = document.getElementById('mini-player-play');
    const miniPrev = document.getElementById('mini-player-prev');
    const miniNext = document.getElementById('mini-player-next');
    const miniSlider = document.getElementById('mini-player-slider');
    const miniCurrent = document.getElementById('mini-player-current');
    const miniDuration = document.getElementById('mini-player-duration');

    let miniTrackList = Array.from(document.querySelectorAll('.track-item[data-preview]'));
    let miniCurrentIdx = -1;

    function formatTime(sec) {
        if (isNaN(sec)) return '0:00';
        const m = Math.floor(sec / 60);
        const s = Math.floor(sec % 60);
        return m + ':' + (s < 10 ? '0' : '') + s;
    }

    function showMiniPlayer(idx) {
        if (!miniPlayer || !miniTrackList[idx]) return;
        const item = miniTrackList[idx];
        const img = item.querySelector('.track-cover img');
        miniImg.src = img ? img.src : '';
        miniImg.alt = img ? img.alt : '';
        miniTitle.textContent = item.querySelector('h3')?.textContent || '';
        miniMeta.textContent = item.querySelector('.track-info')?.textContent || '';
        miniPlayer.hidden = false;
        miniCurrentIdx = idx;
        updateMiniPlayerProgress();
        updateMiniPlayerPlayIcon();
        attachAudioEvents();
    }

    function hideMiniPlayer() {
        if (miniPlayer) miniPlayer.hidden = true;
        miniCurrentIdx = -1;
    }

    function playMiniTrack(idx) {
        if (miniTrackList[idx]) {
            miniCurrentIdx = idx;
            miniTrackList[idx].querySelector('.track-play-btn')?.click();
            showMiniPlayer(idx);
        }
    }

    function updateMiniPlayerProgress() {
        const audio = document.getElementById('artist-audio-preview');
        if (!audio) return;
        miniSlider.max = Math.floor(audio.duration) || 1;
        miniSlider.value = Math.floor(audio.currentTime) || 0;
        miniCurrent.textContent = formatTime(audio.currentTime);
        miniDuration.textContent = formatTime(audio.duration);
    }

    function updateMiniPlayerPlayIcon() {
        const audio = document.getElementById('artist-audio-preview');
        if (!audio) return;
        const icon = miniPlay.querySelector('i');
        if (audio.paused) {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
        } else {
            icon.classList.remove('fa-play');
            icon.classList.add('fa-pause');
        }
    }

    function attachAudioEvents() {
        const audio = document.getElementById('artist-audio-preview');
        if (!audio) return;
        // Remove previous listeners to avoid duplicates
        audio.onplay = updateMiniPlayerPlayIcon;
        audio.onpause = updateMiniPlayerPlayIcon;
        audio.ontimeupdate = updateMiniPlayerProgress;
        audio.onended = function () {
            if (miniCurrentIdx < miniTrackList.length - 1) playMiniTrack(miniCurrentIdx + 1);
            else hideMiniPlayer();
        };
        audio.onloadedmetadata = updateMiniPlayerProgress;
        audio.onseeked = updateMiniPlayerProgress;
        audio.onvolumechange = updateMiniPlayerProgress;
    }

    if (miniPlayer) {
        miniPlay.addEventListener('click', () => {
            const audio = document.getElementById('artist-audio-preview');
            if (audio) {
                if (audio.paused) audio.play();
                else audio.pause();
            }
        });
        miniPrev.addEventListener('click', () => {
            if (miniCurrentIdx > 0) playMiniTrack(miniCurrentIdx - 1);
        });
        miniNext.addEventListener('click', () => {
            if (miniCurrentIdx < miniTrackList.length - 1) playMiniTrack(miniCurrentIdx + 1);
        });
        miniSlider.addEventListener('input', () => {
            const audio = document.getElementById('artist-audio-preview');
            if (audio) audio.currentTime = miniSlider.value;
        });
    }

    // Hook into track play to show mini player
    document.querySelectorAll('.track-play-btn').forEach((btn, idx) => {
        btn.addEventListener('click', () => {
            showMiniPlayer(idx);
        });
    });

    // If a track is already playing on load, show the mini player
    setTimeout(() => {
        const audio = document.getElementById('artist-audio-preview');
        if (audio && !audio.paused && miniTrackList.length) {
            // Try to find the playing track
            let idx = miniTrackList.findIndex(item => item.classList.contains('playing'));
            if (idx === -1) idx = 0;
            showMiniPlayer(idx);
        }
    }, 300);
});
