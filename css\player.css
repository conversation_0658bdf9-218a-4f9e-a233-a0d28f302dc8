:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    --player-bg: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%);
    --player-card: rgba(255,255,255,0.04);
    --player-border: 1.5px solid rgba(255,255,255,0.12);
    --player-radius: 22px;
    --player-shadow: 0 8px 32px rgba(56, 12, 97, 0.18);
    --player-glow: 0 0 24px 6px var(--neon-blue), 0 0 48px 12px var(--cosmic-pink);
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: var(--text-primary); /* Fixed: use --text-primary */
}

/* Navbar Styles */
header {
    background: rgba(19, 21, 26, 0.95); /* Remove gradient, use solid/semi-transparent dark */
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Header gradient enhancement */
header {
    background: rgba(19, 21, 26, 0.95); /* Remove gradient, use solid/semi-transparent dark */
}

/* Player Container */
.player-container {
    max-width: 540px;
    margin: 120px auto 40px auto;
    background: var(--player-bg);
    border-radius: var(--player-radius);
    box-shadow: var(--player-shadow);
    padding: 2.5rem 2rem 2rem 2rem;
    position: relative;
    z-index: 2;
    border: var(--player-border);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.player-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Album Art */
.album-art-container {
    width: 220px;
    height: 220px;
    margin-bottom: 1.5rem;
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    background: rgba(0,0,0,0.12);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}
.album-art {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 18px;
    transition: box-shadow 0.4s, transform 0.4s;
}
.album-art.playing {
    /* Subtle animation: slower, less scale, less glow */
    box-shadow: 0 0 12px 2px var(--neon-blue), 0 0 18px 4px var(--cosmic-pink);
    animation: albumSubtleRotate 32s linear infinite;
}
@keyframes albumSubtleRotate {
    0% {
        transform: rotate(0deg) scale(1);
        box-shadow: 0 0 12px 2px var(--neon-blue), 0 0 18px 4px var(--cosmic-pink);
    }
    50% {
        transform: rotate(180deg) scale(1.025);
        box-shadow: 0 0 18px 4px var(--neon-blue), 0 0 24px 8px var(--cosmic-pink);
    }
    100% {
        transform: rotate(360deg) scale(1);
        box-shadow: 0 0 12px 2px var(--neon-blue), 0 0 18px 4px var(--cosmic-pink);
    }
}

/* Track Info */
.track-info {
    text-align: center;
    margin-bottom: 1.2rem;
}
#song-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.2em;
}
#artist-name {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Progress Bar */
.progress-container {
    width: 100%;
    margin-bottom: 1.2rem;
}
.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255,255,255,0.10);
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}
.progress {
    height: 100%;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 3px;
    width: 0%;
    transition: width 0.2s;
}
.time {
    display: flex;
    justify-content: space-between;
    font-size: 0.97rem;
    color: var(--text-secondary);
    margin-top: 0.3em;
}

/* Player Controls */
.player-controls {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 0.7em;
}
.primary-controls {
    display: flex;
    align-items: center;
    gap: 1.2em;
    margin-bottom: 0.7em;
}
.control-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(255,255,255,0.08);
    color: #fff;
    border: none;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    cursor: pointer;
    outline: none;
}
.control-button:focus,
.control-button:hover {
    background: var(--gradient-primary);
    color: #fff;
    box-shadow: 0 0 0 2px var(--neon-blue);
    transform: scale(1.08);
}
#play-pause {
    background: var(--gradient-primary);
    color: #fff;
    font-size: 1.7rem;
    width: 60px;
    height: 60px;
    box-shadow: 0 4px 16px rgba(0,224,255,0.12);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
}
#play-pause:focus,
#play-pause:hover {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: 0 0 0 2px var(--cosmic-pink), 0 4px 16px rgba(255,0,110,0.12);
    transform: scale(1.12);
}
.play-icon.hidden,
.pause-icon.hidden {
    display: none;
}

/* Additional Controls */
.additional-controls {
    display: flex;
    align-items: center;
    gap: 1em;
    margin-top: 0.2em;
}
.volume-control {
    display: flex;
    align-items: center;
    gap: 0.5em;
    background: rgba(255,255,255,0.05);
    border-radius: 18px;
    padding: 0.3em 0.8em;
}
.volume-control i {
    font-size: 1.1em;
    color: var(--neon-blue);
}
#volume {
    width: 80px;
    accent-color: var(--cosmic-pink);
}
.hd-button {
    background: rgba(255,255,255,0.08);
    color: var(--neon-blue);
    border: none;
    border-radius: 16px;
    padding: 0.4em 1.2em;
    font-weight: 700;
    font-size: 1em;
    margin-left: 0;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}
.hd-button.active,
.hd-button:focus,
.hd-button:hover {
    background: var(--gradient-primary);
    color: #fff;
}

/* Volume control gradient styles */
.volume-control-centered input[type="range"] {
    width: 120px;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    transition: background 0.3s;
}

.volume-control-centered input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: 0 0 8px var(--cosmic-pink), 0 0 8px var(--neon-blue);
    cursor: pointer;
    border: none;
    transition: background 0.3s;
}

.volume-control-centered input[type="range"]:focus::-webkit-slider-thumb,
.volume-control-centered input[type="range"]:hover::-webkit-slider-thumb {
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    box-shadow: 0 0 12px var(--neon-blue), 0 0 12px var(--cosmic-pink);
}

.volume-control-centered input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: 0 0 8px var(--cosmic-pink), 0 0 8px var(--neon-blue);
    cursor: pointer;
    border: none;
    transition: background 0.3s;
}

.volume-control-centered input[type="range"]:focus::-moz-range-thumb,
.volume-control-centered input[type="range"]:hover::-moz-range-thumb {
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    box-shadow: 0 0 12px var(--neon-blue), 0 0 12px var(--cosmic-pink);
}

.volume-control-centered input[type="range"]::-ms-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: 0 0 8px var(--cosmic-pink), 0 0 8px var(--neon-blue);
    cursor: pointer;
    border: none;
    transition: background 0.3s;
}

.volume-control-centered input[type="range"]:focus::-ms-thumb,
.volume-control-centered input[type="range"]:hover::-ms-thumb {
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    box-shadow: 0 0 12px var(--neon-blue), 0 0 12px var(--cosmic-pink);
}

/* Hide default track for Firefox */
.volume-control-centered input[type="range"]::-moz-range-track {
    background: transparent;
}

/* Playlist Section */
.playlist-section {
    width: 100%;
    margin-top: 2em;
    background: var(--player-card);
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 1.2em 1em;
}
.playlist-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.7em;
}
.playlist-header h3 {
    font-size: 1.1em;
    font-weight: 700;
    color: var(--text-primary);
}
#playlist-name {
    color: var(--text-secondary);
    font-size: 1em;
    font-weight: 500;
}
.playlist-container {
    max-height: 180px;
    overflow-y: auto;
}
.playlist {
    list-style: none;
    margin: 0;
    padding: 0;
}
.playlist li {
    padding: 0.6em 0.5em;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.7em;
    cursor: pointer;
    transition: background 0.2s;
}
.playlist li.active,
.playlist li:focus,
.playlist li:hover {
    background: rgba(0,224,255,0.10);
    color: var(--neon-blue);
    outline: none;
}
.playlist li .track-title {
    font-weight: 600;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.playlist li .track-artist {
    font-size: 0.97em;
    color: var(--text-secondary);
    margin-left: 0.5em;
}

/* Lyrics Panel */
.lyrics-panel {
    position: fixed;
    right: 0;
    top: 0;
    width: 370px;
    height: 100vh;
    background: var(--player-bg);
    box-shadow: -8px 0 32px rgba(0,0,0,0.18);
    border-left: var(--player-border);
    z-index: 1002;
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.4,0,0.2,1);
    display: flex;
    flex-direction: column;
    pointer-events: none;
    opacity: 0;
}
.lyrics-panel.show {
    transform: translateX(0);
    pointer-events: auto;
    opacity: 1;
}
.lyrics-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.2em 1em 0.7em 1em;
    border-bottom: 1px solid rgba(255,255,255,0.08);
}
.lyrics-header h3 {
    font-size: 1.2em;
    font-weight: 700;
    color: var(--text-primary);
}
.close-lyrics {
    background: none;
    border: none;
    color: var(--cosmic-pink);
    font-size: 1.3em;
    cursor: pointer;
    transition: color 0.2s;
}
.close-lyrics:hover,
.close-lyrics:focus {
    color: var(--neon-blue);
}
.lyrics-content {
    padding: 1.2em 1em;
    color: var(--text-primary);
    font-size: 1.08em;
    line-height: 1.7;
    overflow-y: auto;
    flex: 1;
}

/* Visualizer styles */
.visualizer-container {
    width: 100%;
    height: 48px;
    margin-bottom: 1.2em;
    background: rgba(0,0,0,0.18);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}
#audioVisualizer {
    width: 100%;
    height: 100%;
    display: block;
    background: transparent;
}

/* Responsive Design */
@media (max-width: 700px) {
    .player-container {
        max-width: 98vw;
        padding: 1.2rem 0.5rem 1rem 0.5rem;
    }
    .album-art-container {
        width: 150px;
        height: 150px;
    }
    .lyrics-panel {
        width: 100vw;
        left: 0;
        right: 0;
        border-radius: 0;
        border-left: none;
        border-top: var(--player-border);
        top: auto;
        bottom: 0;
        height: 60vh;
        max-height: 400px;
        box-shadow: 0 -8px 32px rgba(0,0,0,0.18);
    }
}
@media (max-width: 480px) {
    .player-container {
        padding: 0.7rem 0.2rem 0.7rem 0.2rem;
    }
    .album-art-container {
        width: 100px;
        height: 100px;
    }
    .track-info #song-title {
        font-size: 1.1rem;
    }
    .track-info #artist-name {
        font-size: 0.9rem;
    }
    .lyrics-panel {
        height: 50vh;
        max-height: 260px;
    }
}

/* Playlist item enhancements */
.playlist-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.03);
    transition: all 0.3s ease;
    cursor: pointer;
    animation: slideIn 0.3s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}
.playlist-item:nth-child(1) { animation-delay: 0.1s; }
.playlist-item:nth-child(2) { animation-delay: 0.2s; }
.playlist-item:nth-child(3) { animation-delay: 0.3s; }
.playlist-item:nth-child(4) { animation-delay: 0.4s; }
.playlist-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.playlist-item:hover,
.playlist-item:focus,
.playlist-item.active {
    background: rgba(0,224,255,0.10);
    color: var(--neon-blue);
    outline: none;
    transform: translateX(5px);
}

.playlist-item-info {
    flex: 1;
    transition: transform 0.3s ease;
}

.playlist-item:hover .playlist-item-info,
.playlist-item:focus .playlist-item-info {
    transform: translateX(5px);
}

.playlist-item-title {
    font-weight: 600;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1em;
}

.playlist-item-artist {
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
    margin: 2px 0 0 0;
}

.playlist-item-duration {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.85em;
    padding-left: 15px;
}

/* Playlist item active/playing indicator */
.playlist-item.playing {
    background: linear-gradient(90deg, rgba(0,224,255,0.13), rgba(255,0,110,0.10));
    color: var(--neon-blue);
    position: relative;
    box-shadow: 0 0 8px 0 rgba(0,224,255,0.10);
}
.playlist-item .playing-indicator {
    display: inline-block;
    margin-left: 10px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    box-shadow: 0 0 8px var(--neon-blue), 0 0 8px var(--cosmic-pink);
    animation: playingPulse 1.2s infinite alternate;
    vertical-align: middle;
}
@keyframes playingPulse {
    0% { opacity: 0.7; box-shadow: 0 0 8px var(--neon-blue), 0 0 8px var(--cosmic-pink);}
    100% { opacity: 1; box-shadow: 0 0 16px var(--neon-blue), 0 0 16px var(--cosmic-pink);}
}

/* Tooltip styles */
[data-tooltip] {
    position: relative;
}
[data-tooltip]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-8px);
    padding: 6px 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 12px;
    border-radius: 6px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    pointer-events: none;
    z-index: 10;
}
[data-tooltip]:hover::before,
[data-tooltip]:focus::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

/* Review complete: code is organized and uses variables consistently. */
/* No redundant or unused selectors found. */